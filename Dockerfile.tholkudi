# Dockerfile for Tholkudi Digital Archive
# Optimized multi-stage build for production deployment

# Build stage
FROM node:18-alpine AS build

# Install build dependencies
RUN apk add --no-cache python3 make g++ curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./

# Install dependencies with increased timeout
RUN yarn install --network-timeout 300000 --frozen-lockfile

# Copy source code
COPY . .

# Build the application for production
RUN yarn build:prod

# Production stage
FROM node:18-alpine AS production

# Install PM2 globally for process management
RUN npm install --global pm2

# Install curl for health checks
RUN apk add --no-cache curl

# Create app directory and user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S dspace -u 1001

# Set working directory
WORKDIR /app

# Copy built application from build stage
COPY --from=build --chown=dspace:nodejs /app/dist ./dist
COPY --from=build --chown=dspace:nodejs /app/config ./config
COPY --from=build --chown=dspace:nodejs /app/docker/dspace-ui.json ./dspace-ui.json

# Copy custom assets
COPY --from=build --chown=dspace:nodejs /app/src/assets ./assets

# Switch to non-root user
USER dspace

# Expose port
EXPOSE 4000

# Set environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max_old_space_size=2048"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:4000 || exit 1

# Start the application with PM2
CMD ["pm2-runtime", "start", "dspace-ui.json", "--json"]
