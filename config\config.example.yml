# NOTE: will log all redux actions and transfers in console
debug: false

# Angular User Inteface settings
# NOTE: these settings define where Node.js will start your UI application. Therefore, these
# "ui" settings usually specify a localhost port/URL which is later proxied to a public URL (using Apache or similar)
ui:
  ssl: false
  host: localhost
  port: 4000
  # NOTE: Space is capitalized because 'namespace' is a reserved string in TypeScript
  nameSpace: /
  # The rateLimiter settings limit each IP to a 'max' of 500 requests per 'windowMs' (1 minute).
  rateLimiter:
    windowMs: 60000 # 1 minute
    max: 500 # limit each IP to 500 requests per windowMs
  # Trust X-FORWARDED-* headers from proxies (default = true)
  useProxies: true

# Angular Server Side Rendering (SSR) settings
ssr:
  # Whether to tell Angular to inline "critical" styles into the server-side rendered HTML.
  # Determining which styles are critical is a relatively expensive operation; this option is
  # disabled (false) by default to boost server performance at the expense of loading smoothness.
  inlineCriticalCss: false
  # Path prefixes to enable SSR for. By default these are limited to paths of primary DSpace objects.
  # NOTE: The "/handle/" path ensures <PERSON>le redirects work via SSR.  The "/reload/" path ensures
  # hard refreshes (e.g. after login) trigger SSR while fully reloading the page.
  paths: [ '/home', '/items/', '/entities/', '/collections/', '/communities/', '/bitstream/', '/bitstreams/', '/handle/', '/reload/' ]
  # Whether to enable rendering of Search component on SSR.
  # If set to true the component will be included in the HTML returned from the server side rendering.
  # If set to false the component will not be included in the HTML returned from the server side rendering.
  enableSearchComponent: false
  # Whether to enable rendering of Browse component on SSR.
  # If set to true the component will be included in the HTML returned from the server side rendering.
  # If set to false the component will not be included in the HTML returned from the server side rendering.
  enableBrowseComponent: false
  # Enable state transfer from the server-side application to the client-side application.
  # Defaults to true.
  # Note: When using an external application cache layer, it's recommended not to transfer the state to avoid caching it.
  # Disabling it ensures that dynamic state information is not inadvertently cached, which can improve security and
  # ensure that users always use the most up-to-date state.
  transferState: true
  # When a different REST base URL is used for the server-side application, the generated state contains references to
  # REST resources with the internal URL configured. By default, these internal URLs are replaced with public URLs.
  # Disable this setting to avoid URL replacement during SSR. In this the state is not transferred to avoid security issues.
  replaceRestUrl: true
  # Enable request performance profiling data collection and printing the results in the server console.
  # Defaults to false. Enabling in production is NOT recommended
  #enablePerformanceProfiler: false

# The REST API server settings
# NOTE: these settings define which (publicly available) REST API to use. They are usually
# 'synced' with the 'dspace.server.url' setting in your backend's local.cfg.
rest:
  ssl: true
  host: sandbox.dspace.org
  port: 443
  # NOTE: Space is capitalized because 'namespace' is a reserved string in TypeScript
  nameSpace: /server
  # Provide a different REST url to be used during SSR execution. It must contain the whole url including protocol, server port and
  # server namespace (uncomment to use it).
  #ssrBaseUrl: http://localhost:8080/server

# Caching settings
cache:
  # NOTE: how long should objects be cached for by default
  msToLive:
    default: 900000 # 15 minutes
  # Default 'Cache-Control' HTTP Header to set for all static content (including compiled *.js files)
  # Defaults to max-age=604,800 seconds (one week). This lets a user's browser know that it can cache these
  # files for one week, after which they will be "stale" and need to be redownloaded.
  # NOTE: When updates are made to compiled *.js files, it will automatically bypass this browser cache, because
  # all compiled *.js files include a unique hash in their name which updates when content is modified.
  control: max-age=604800 # revalidate browser
  autoSync:
    defaultTime: 0
    maxBufferSize: 100
    timePerMethod:
      PATCH: 3 # time in seconds
  # In-memory cache(s) of server-side rendered pages. These caches will store the most recently accessed public pages.
  # Pages are automatically added/dropped from these caches based on how recently they have been used.
  # Restarting the app clears all page caches.
  # NOTE: To control the cache size, use the "max" setting. Keep in mind, individual cached pages are usually small (<100KB).
  # Enabling *both* caches will mean that a page may be cached twice, once in each cache (but may expire at different times via timeToLive).
  serverSide:
    # Set to true to see all cache hits/misses/refreshes in your console logs. Useful for debugging SSR caching issues.
    debug: false
    # When enabled (i.e. max > 0), known bots will be sent pages from a server side cache specific for bots.
    # (Keep in mind, bot detection cannot be guarranteed. It is possible some bots will bypass this cache.)
    botCache:
      # Maximum number of pages to cache for known bots. Set to zero (0) to disable server side caching for bots.
      # Default is 1000, which means the 1000 most recently accessed public pages will be cached.
      # As all pages are cached in server memory, increasing this value will increase memory needs.
      # Individual cached pages are usually small (<100KB), so max=1000 should only require ~100MB of memory.
      max: 1000
      # Amount of time after which cached pages are considered stale (in ms). After becoming stale, the cached
      # copy is automatically refreshed on the next request.
      # NOTE: For the bot cache, this setting may impact how quickly search engine bots will index new content on your site.
      # For example, setting this to one week may mean that search engine bots may not find all new content for one week.
      timeToLive: 86400000 # 1 day
      # When set to true, after timeToLive expires, the next request will receive the *cached* page & then re-render the page
      # behind the scenes to update the cache. This ensures users primarily interact with the cache, but may receive stale pages (older than timeToLive).
      # When set to false, after timeToLive expires, the next request will wait on SSR to complete & receive a fresh page (which is then saved to cache).
      # This ensures stale pages (older than timeToLive) are never returned from the cache, but some users will wait on SSR.
      allowStale: true
    # When enabled (i.e. max > 0), all anonymous users will be sent pages from a server side cache.
    # This allows anonymous users to interact more quickly with the site, but also means they may see slightly
    # outdated content (based on timeToLive)
    anonymousCache:
      # Maximum number of pages to cache. Default is zero (0) which means anonymous user cache is disabled.
      # As all pages are cached in server memory, increasing this value will increase memory needs.
      # Individual cached pages are usually small (<100KB), so a value of max=1000 would only require ~100MB of memory.
      max: 0
      # Amount of time after which cached pages are considered stale (in ms). After becoming stale, the cached
      # copy is automatically refreshed on the next request.
      # NOTE: For the anonymous cache, it is recommended to keep this value low to avoid anonymous users seeing outdated content.
      timeToLive: 10000 # 10 seconds
      # When set to true, after timeToLive expires, the next request will receive the *cached* page & then re-render the page
      # behind the scenes to update the cache. This ensures users primarily interact with the cache, but may receive stale pages (older than timeToLive).
      # When set to false, after timeToLive expires, the next request will wait on SSR to complete & receive a fresh page (which is then saved to cache).
      # This ensures stale pages (older than timeToLive) are never returned from the cache, but some users will wait on SSR.
      allowStale: true

# Authentication settings
auth:
  # Authentication UI settings
  ui:
    # the amount of time before the idle warning is shown
    timeUntilIdle: 900000 # 15 minutes
    # the amount of time the user has to react after the idle warning is shown before they are logged out.
    idleGracePeriod: 300000 # 5 minutes
  # Authentication REST settings
  rest:
    # If the rest token expires in less than this amount of time, it will be refreshed automatically.
    # This is independent from the idle warning.
    timeLeftBeforeTokenRefresh: 120000 # 2 minutes

# Form settings
form:
  # Sets the spellcheck textarea attribute value
  spellCheck: true
  # NOTE: Map server-side validators to comparative Angular form validators
  validatorMap:
    required: required
    regex: pattern

# Notification settings
notifications:
  rtl: false
  position:
    - top
    - right
  maxStack: 8
  # NOTE: after how many seconds notification is closed automatically. If set to zero notifications are not closed automatically
  timeOut: 5000 # 5 second
  clickToClose: true
  # NOTE: 'fade' | 'fromTop' | 'fromRight' | 'fromBottom' | 'fromLeft' | 'rotate' | 'scale'
  animate: scale

# Submission settings
submission:
  autosave:
    # NOTE: which metadata trigger an autosave
    metadata: []
    # NOTE: after how many time (milliseconds) submission is saved automatically
    # eg. timer: 5 * (1000 * 60); // 5 minutes
    timer: 0
  # Always show the duplicate detection section if enabled, even if there are no potential duplicates detected
  # (a message will be displayed to indicate no matches were found)
  duplicateDetection:
    alwaysShowSection: false
  icons:
    metadata:
      # NOTE: example of configuration
      #   # NOTE: metadata name
      # - name: dc.author
      #   # NOTE: fontawesome (v6.x) icon classes and bootstrap utility classes can be used
      #   style: fas fa-user
      - name: dc.author
        style: fas fa-user
      # default configuration
      - name: default
        style: ''
    authority:
      confidence:
        # NOTE: example of configuration
        #   # NOTE: confidence value
        # - value: 600
        #   # NOTE: fontawesome (v6.x) icon classes and bootstrap utility classes can be used
        #   style: text-success
        #   icon: fa-circle-check
        #   # NOTE: the class configured in property style is used by default, the icon property could be used in component
        #           configured to use a 'icon mode' display (mainly in edit-item page)
        - value: 600
          style: text-success
          icon: fa-circle-check
        - value: 500
          style: text-info
          icon: fa-gear
        - value: 400
          style: text-warning
          icon: fa-circle-question
        - value: 300
          style: text-muted
          icon: fa-thumbs-down
        - value: 200
          style: text-muted
          icon: fa-circle-exclamation
        - value: 100
          style: text-muted
          icon: fa-circle-stop
        - value: 0
          style: text-muted
          icon: fa-ban
        - value: -1
          style: text-muted
          icon: fa-circle-xmark
        # default configuration
        - value: default
          style: text-muted
          icon: fa-circle-xmark

#  Default Language in which the UI will be rendered if the user's browser language is not an active language
defaultLanguage: en

# Languages. DSpace Angular holds a message catalog for each of the following languages.
# When set to active, users will be able to switch to the use of this language in the user interface.
languages:
  - code: en
    label: English
    active: true
  - code: ar
    label: العربية
    active: true
  - code: bn
    label: বাংলা
    active: true
  - code: ca
    label: Català
    active: true
  - code: cs
    label: Čeština
    active: true
  - code: de
    label: Deutsch
    active: true
  - code: el
    label: Ελληνικά
    active: true
  - code: es
    label: Español
    active: true
  - code: fi
    label: Suomi
    active: true
  - code: fr
    label: Français
    active: true
  - code: gd
    label: Gàidhlig
    active: true
  - code: hi
    label: हिंदी
    active: true
  - code: hu
    label: Magyar
    active: true
  - code: it
    label: Italiano
    active: true
  - code: kk
    label: Қазақ
    active: true
  - code: lv
    label: Latviešu
    active: true
  - code: nl
    label: Nederlands
    active: true
  - code: pl
    label: Polski
    active: true
  - code: pt-PT
    label: Português
    active: true
  - code: pt-BR
    label: Português do Brasil
    active: true
  - code: sr-lat
    label: Srpski (lat)
    active: true
  - code: sr-cyr
    label: Српски
    active: true
  - code: sv
    label: Svenska
    active: true
  - code: tr
    label: Türkçe
    active: true
  - code: uk
    label: Yкраї́нська
    active: true
  - code: vi
    label: Tiếng Việt
    active: true


# Browse-By Pages
browseBy:
  # Amount of years to display using jumps of one year (current year - oneYearLimit)
  oneYearLimit: 10
  # Limit for years to display using jumps of five years (current year - fiveYearLimit)
  fiveYearLimit: 30
  # The absolute lowest year to display in the dropdown (only used when no lowest date can be found for all items)
  defaultLowerLimit: 1900
  # If true, thumbnail images for items will be added to BOTH search and browse result lists.
  showThumbnails: true
  # The number of entries in a paginated browse results list.
  # Rounded to the nearest size in the list of selectable sizes on the
  # settings menu.
  pageSize: 20

communityList:
  # No. of communities to list per expansion (show more)
  pageSize: 20

homePage:
  recentSubmissions:
    # The number of item showing in recent submission components
    pageSize: 5
    # Sort record of recent submission
    sortField: 'dc.date.accessioned'
  topLevelCommunityList:
    # No. of communities to list per page on the home page
    # This will always round to the nearest number from the list of page sizes. e.g. if you set it to 7 it'll use 10
    pageSize: 5
  # Enable or disable the Discover filters on the homepage
  showDiscoverFilters: false

# Item Config
item:
  edit:
    undoTimeout: 10000 # 10 seconds
  # Show the item access status label in items lists
  showAccessStatuses: false
  bitstream:
    # Number of entries in the bitstream list in the item view page.
    # Rounded to the nearest size in the list of selectable sizes on the
    # settings menu.  See pageSizeOptions in 'pagination-component-options.model.ts'.
    pageSize: 5

# Community Page Config
community:
  # Search tab config
  searchSection:
    showSidebar: true

# Collection Page Config
collection:
  # Search tab config
  searchSection:
    showSidebar: true
  edit:
    undoTimeout: 10000 # 10 seconds

# Theme Config
themes:
  # Add additional themes here. In the case where multiple themes match a route, the first one
  # in this list will get priority. It is advisable to always have a theme that matches
  # every route as the last one
  #
  # # A theme with a handle property will match the community, collection or item with the given
  # # handle, and all collections and/or items within it
  # - name: custom
  #   handle: 10673/1233
  #
  # # A theme with a regex property will match the route using a regular expression. If it
  # # matches the route for a community or collection it will also apply to all collections
  # # and/or items within it
  # - name: custom
  #   regex: collections\/e8043bc2.*
  #
  # # A theme with a uuid property will match the community, collection or item with the given
  # # ID, and all collections and/or items within it
  # - name: custom
  #   uuid: 0958c910-2037-42a9-81c7-dca80e3892b4
  #
  # # The extends property specifies an ancestor theme (by name). Whenever a themed component is not found
  # # in the current theme, its ancestor theme(s) will be checked recursively before falling back to default.
  # - name: custom-A
  #   extends: custom-B
  #   # Any of the matching properties above can be used
  #   handle: 10673/34
  #
  # - name: custom-B
  #   extends: custom
  #   handle: 10673/12
  #
  # # A theme with only a name will match every route
  # name: custom
  #
  # # This theme will use the default bootstrap styling for DSpace components
  # - name: BASE_THEME_NAME
  #
  - name: dspace
    headTags:
    - tagName: link
      attributes:
        rel: icon
        href: assets/dspace/images/favicons/favicon.ico
        sizes: any
    - tagName: link
      attributes:
        rel: icon
        href: assets/dspace/images/favicons/favicon.svg
        type: image/svg+xml
    - tagName: link
      attributes:
        rel: apple-touch-icon
        href: assets/dspace/images/favicons/apple-touch-icon.png
    - tagName: link
      attributes:
        rel: manifest
        href: assets/dspace/images/favicons/manifest.webmanifest

# The default bundles that should always be displayed as suggestions when you upload a new bundle
bundle:
  standardBundles: [ ORIGINAL, THUMBNAIL, LICENSE ]

# Whether to enable media viewer for image and/or video Bitstreams (i.e. Bitstreams whose MIME type starts with 'image' or 'video').
# For images, this enables a gallery viewer where you can zoom or page through images.
# For videos, this enables embedded video streaming
mediaViewer:
  image: false
  video: false

# Whether the end user agreement is required before users use the repository.
# If enabled, the user will be required to accept the agreement before they can use the repository.
# And whether the privacy statement/COAR notify support page should exist or not.
info:
  enableEndUserAgreement: true
  enablePrivacyStatement: true
  enableCOARNotifySupport: true

# Whether to enable Markdown (https://commonmark.org/) and MathJax (https://www.mathjax.org/)
# display in supported metadata fields. By default, only dc.description.abstract is supported.
markdown:
  enabled: false
  mathjax: false

# Which vocabularies should be used for which search filters
# and whether to show the filter in the search sidebar
# Take a look at the filter-vocabulary-config.ts file for documentation on how the options are obtained
vocabularies:
  - filter: 'subject'
    vocabulary: 'srsc'
    enabled: true

# Default collection/community sorting order at Advanced search, Create/update community and collection when there are not a query.
comcolSelectionSort:
  sortField: 'dc.title'
  sortDirection: 'ASC'

# Example of fallback collection for suggestions import
# suggestion:
  # - collectionId: 8f7df5ca-f9c2-47a4-81ec-8a6393d6e5af
    # source: "openaire"


# Search settings
search:
  # Settings to enable/disable or configure advanced search filters.
  advancedFilters:
    enabled: false
    # List of filters to enable in "Advanced Search" dropdown
    filter: [ 'title', 'author', 'subject', 'entityType' ]
  #
  # Number used to render n UI elements called loading skeletons that act as placeholders.
  # These elements indicate that some content will be loaded in their stead.
  # Since we don't know how many filters will be loaded before we receive a response from the server we use this parameter for the skeletons count.
  # e.g. If we set 5 then 5 loading skeletons will be visualized before the actual filters are retrieved.
  defaultFiltersCount: 5


# Notify metrics
# Configuration for Notify Admin Dashboard for metrics visualization
notifyMetrics:
  # Configuration for received messages
- title: 'admin-notify-dashboard.received-ldn'
  boxes:
  - color: '#B8DAFF'
    title: 'admin-notify-dashboard.NOTIFY.incoming.accepted'
    config: 'NOTIFY.incoming.accepted'
    description: 'admin-notify-dashboard.NOTIFY.incoming.accepted.description'
  - color: '#D4EDDA'
    title: 'admin-notify-dashboard.NOTIFY.incoming.processed'
    config: 'NOTIFY.incoming.processed'
    description: 'admin-notify-dashboard.NOTIFY.incoming.processed.description'
  - color: '#FDBBC7'
    title: 'admin-notify-dashboard.NOTIFY.incoming.failure'
    config: 'NOTIFY.incoming.failure'
    description: 'admin-notify-dashboard.NOTIFY.incoming.failure.description'
  - color: '#FDBBC7'
    title: 'admin-notify-dashboard.NOTIFY.incoming.untrusted'
    config: 'NOTIFY.incoming.untrusted'
    description: 'admin-notify-dashboard.NOTIFY.incoming.untrusted.description'
  - color: '#43515F'
    title: 'admin-notify-dashboard.NOTIFY.incoming.involvedItems'
    textColor: '#fff'
    config: 'NOTIFY.incoming.involvedItems'
    description: 'admin-notify-dashboard.NOTIFY.incoming.involvedItems.description'
# Configuration for outgoing messages
- title: 'admin-notify-dashboard.generated-ldn'
  boxes:
  - color: '#B8DAFF'
    title: 'admin-notify-dashboard.NOTIFY.outgoing.queued'
    config: 'NOTIFY.outgoing.queued'
    description: 'admin-notify-dashboard.NOTIFY.outgoing.queued.description'
  - color: '#FDEEBB'
    title: 'admin-notify-dashboard.NOTIFY.outgoing.queued_for_retry'
    config: 'NOTIFY.outgoing.queued_for_retry'
    description: 'admin-notify-dashboard.NOTIFY.outgoing.queued_for_retry.description'
  - color: '#FDBBC7'
    title: 'admin-notify-dashboard.NOTIFY.outgoing.failure'
    config: 'NOTIFY.outgoing.failure'
    description: 'admin-notify-dashboard.NOTIFY.outgoing.failure.description'
  - color: '#43515F'
    title: 'admin-notify-dashboard.NOTIFY.outgoing.involvedItems'
    textColor: '#fff'
    config: 'NOTIFY.outgoing.involvedItems'
    description: 'admin-notify-dashboard.NOTIFY.outgoing.involvedItems.description'
  - color: '#D4EDDA'
    title: 'admin-notify-dashboard.NOTIFY.outgoing.delivered'
    config: 'NOTIFY.outgoing.delivered'
    description: 'admin-notify-dashboard.NOTIFY.outgoing.delivered.description'


# Live Region configuration
# Live Region as defined by w3c, https://www.w3.org/TR/wai-aria-1.1/#terms:
# Live regions are perceivable regions of a web page that are typically updated as a
# result of an external event when user focus may be elsewhere.
#
# The DSpace live region is a component present at the bottom of all pages that is invisible by default, but is useful
# for screen readers. Any message pushed to the live region will be announced by the screen reader. These messages
# usually contain information about changes on the page that might not be in focus.
liveRegion:
  # The duration after which messages disappear from the live region in milliseconds
  messageTimeOutDurationMs: 30000
  # The visibility of the live region. Setting this to true is only useful for debugging purposes.
  isVisible: false
