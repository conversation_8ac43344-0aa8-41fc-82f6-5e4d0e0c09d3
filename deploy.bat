@echo off
REM Tholkudi Digital Archive Deployment Script for Windows
REM Usage: deploy.bat [dev|prod|stop|logs|status]

setlocal enabledelayedexpansion

set PROJECT_NAME=tholkudi-digital-archive
set DEV_COMPOSE=docker-compose.tholkudi.yml
set PROD_COMPOSE=docker-compose.tholkudi.prod.yml

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed. Please install it and try again.
    exit /b 1
)

if "%1"=="dev" goto deploy_dev
if "%1"=="prod" goto deploy_prod
if "%1"=="stop" goto stop_services
if "%1"=="logs" goto show_logs
if "%1"=="status" goto show_status
goto show_help

:deploy_dev
echo [INFO] Starting Tholkudi Digital Archive in development mode...
if not exist "%DEV_COMPOSE%" (
    echo [ERROR] Development compose file not found: %DEV_COMPOSE%
    exit /b 1
)
docker-compose -f "%DEV_COMPOSE%" down --remove-orphans
docker-compose -f "%DEV_COMPOSE%" up --build -d
echo [SUCCESS] Development environment started!
echo [INFO] Application will be available at: http://localhost:4000
echo [INFO] Live reload is enabled for development
echo [INFO] Use 'deploy.bat logs' to view logs
goto end

:deploy_prod
echo [INFO] Starting Tholkudi Digital Archive in production mode...
if not exist "%PROD_COMPOSE%" (
    echo [ERROR] Production compose file not found: %PROD_COMPOSE%
    exit /b 1
)
echo [WARNING] Make sure to configure DSPACE_REST_HOST in %PROD_COMPOSE%
set /p continue="Continue with production deployment? (y/N): "
if /i not "%continue%"=="y" (
    echo [INFO] Deployment cancelled.
    exit /b 0
)
docker-compose -f "%PROD_COMPOSE%" down --remove-orphans
docker-compose -f "%PROD_COMPOSE%" up --build -d
echo [SUCCESS] Production environment started!
echo [INFO] Application is available at: http://localhost:4000
echo [INFO] Use 'deploy.bat logs' to view logs
goto end

:stop_services
echo [INFO] Stopping Tholkudi Digital Archive services...
if exist "%DEV_COMPOSE%" (
    docker-compose -f "%DEV_COMPOSE%" down --remove-orphans
)
if exist "%PROD_COMPOSE%" (
    docker-compose -f "%PROD_COMPOSE%" down --remove-orphans
)
echo [SUCCESS] All services stopped.
goto end

:show_logs
echo [INFO] Showing logs for Tholkudi Digital Archive...
docker ps --format "table {{.Names}}" | findstr "%PROJECT_NAME%" >nul
if errorlevel 1 (
    echo [WARNING] No running containers found for %PROJECT_NAME%
    goto end
)
docker ps --format "table {{.Names}}" | findstr "%PROJECT_NAME%-prod" >nul
if not errorlevel 1 (
    docker-compose -f "%PROD_COMPOSE%" logs -f
) else (
    docker-compose -f "%DEV_COMPOSE%" logs -f
)
goto end

:show_status
echo [INFO] Checking status of Tholkudi Digital Archive...
echo.
echo Docker Containers:
docker ps --filter "name=%PROJECT_NAME%" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
echo Container Health:
for /f "tokens=*" %%i in ('docker ps --format "{{.Names}}" ^| findstr "%PROJECT_NAME%"') do (
    for /f "tokens=*" %%j in ('docker inspect --format="{{.State.Health.Status}}" "%%i" 2^>nul') do (
        echo %%i: %%j
    )
)
echo.
echo Application URLs:
echo Development: http://localhost:4000
echo Production:  http://localhost:4000
goto end

:show_help
echo Tholkudi Digital Archive Deployment Script
echo.
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   dev     Start in development mode (with live reload)
echo   prod    Start in production mode (optimized build)
echo   stop    Stop all services
echo   logs    Show application logs
echo   status  Show service status
echo   help    Show this help message
echo.
echo Examples:
echo   %0 dev          # Start development environment
echo   %0 prod         # Start production environment
echo   %0 logs         # View logs
echo   %0 stop         # Stop all services

:end
endlocal
