# DSpace Docker image build for hub.docker.com
name: Docker images

# Run this Build for all pushes to 'main' or maintenance branches, or tagged releases.
# Also run for PRs to ensure PR doesn't break Docker build process
# NOTE: uses "reusable-docker-build.yml" in DSpace/DSpace to actually build each of the Docker images
# https://github.com/DSpace/DSpace/blob/main/.github/workflows/reusable-docker-build.yml
# 
on:
  push:
    branches:
      - main
      - 'dspace-**'
    tags:
      - 'dspace-**'
  pull_request:

permissions:
  contents: read  # to fetch code (actions/checkout)
  packages: write # to write images to GitHub Container Registry (GHCR)

jobs:
  #############################################################
  # Build/Push the 'dspace/dspace-angular' image
  #############################################################
  dspace-angular:
    # Ensure this job never runs on forked repos. It's only executed for 'dspace/dspace-angular'
    if: github.repository == 'dspace/dspace-angular'
    # Use the reusable-docker-build.yml script from DSpace/DSpace repo to build our Docker image
    uses: DSpace/DSpace/.github/workflows/reusable-docker-build.yml@main
    with:
      build_id: dspace-angular-dev
      image_name: dspace/dspace-angular
      dockerfile_path: ./Dockerfile
    secrets:
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_ACCESS_TOKEN: ${{ secrets.DOCKER_ACCESS_TOKEN }}

  #############################################################
  # Build/Push the 'dspace/dspace-angular' image ('-dist' tag)
  #############################################################
  dspace-angular-dist:
    # Ensure this job never runs on forked repos. It's only executed for 'dspace/dspace-angular'
    if: github.repository == 'dspace/dspace-angular'
    # Use the reusable-docker-build.yml script from DSpace/DSpace repo to build our Docker image
    uses: DSpace/DSpace/.github/workflows/reusable-docker-build.yml@main
    with:
      build_id: dspace-angular-dist
      image_name: dspace/dspace-angular
      dockerfile_path: ./Dockerfile.dist
      # As this is a "dist" image, its tags are all suffixed with "-dist". Otherwise, it uses the same
      # tagging logic as the primary 'dspace/dspace-angular' image above.
      tags_flavor: suffix=-dist
    secrets:
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_ACCESS_TOKEN: ${{ secrets.DOCKER_ACCESS_TOKEN }}
      # Enable redeploy of sandbox & demo if the branch for this image matches the deployment branch of
      # these sites as specified in reusable-docker-build.xml
      REDEPLOY_SANDBOX_URL:  ${{ secrets.REDEPLOY_SANDBOX_URL }}
      REDEPLOY_DEMO_URL:  ${{ secrets.REDEPLOY_DEMO_URL }}