# Tholkudi Digital Archive Environment Configuration
# Copy this file to .env and modify the values as needed

# =============================================================================
# UI Configuration
# =============================================================================

# Host and port for the Angular UI
DSPACE_UI_HOST=0.0.0.0
DSPACE_UI_PORT=4000
DSPACE_UI_NAMESPACE=/

# SSL configuration for UI
DSPACE_UI_SSL=false

# =============================================================================
# DSpace REST API Configuration
# =============================================================================

# DSpace REST API backend configuration
# For development, use local backend
DSPACE_REST_HOST=localhost
DSPACE_REST_PORT=8080
DSPACE_REST_NAMESPACE=/server
DSPACE_REST_SSL=false

# For production, use your public DSpace backend
# DSPACE_REST_HOST=your-dspace-backend.com
# DSPACE_REST_PORT=443
# DSPACE_REST_NAMESPACE=/server
# DSPACE_REST_SSL=true

# =============================================================================
# Node.js Configuration
# =============================================================================

# Node environment (development/production)
NODE_ENV=development

# Node.js memory options (adjust based on your server capacity)
NODE_OPTIONS=--max_old_space_size=4096

# =============================================================================
# Theme Configuration
# =============================================================================

# Active theme name
THEME_NAME=custom

# =============================================================================
# Docker Configuration
# =============================================================================

# Docker registry and image settings
DOCKER_REGISTRY=docker.io
DOCKER_OWNER=tholkudi
DSPACE_VER=latest

# Container name
CONTAINER_NAME=tholkudi-digital-archive

# =============================================================================
# Security Configuration
# =============================================================================

# CORS settings (if needed)
# CORS_ALLOWED_ORIGINS=http://localhost:4000,https://your-domain.com

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# Performance Configuration
# =============================================================================

# PM2 cluster mode settings (for production)
PM2_INSTANCES=max
PM2_EXEC_MODE=cluster

# =============================================================================
# Database Configuration (if using local DSpace backend)
# =============================================================================

# PostgreSQL configuration
# POSTGRES_DB=dspace
# POSTGRES_USER=dspace
# POSTGRES_PASSWORD=dspace
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432

# =============================================================================
# Email Configuration (if using local DSpace backend)
# =============================================================================

# SMTP settings
# MAIL_SERVER=smtp.gmail.com
# MAIL_SERVER_PORT=587
# MAIL_SERVER_USERNAME=<EMAIL>
# MAIL_SERVER_PASSWORD=your-app-password
# MAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# Additional Configuration
# =============================================================================

# Custom configuration file path
# CONFIG_FILE_PATH=./config/config.yml

# Asset path for custom themes
# ASSET_PATH=./src/assets

# Enable/disable features
# ENABLE_ANALYTICS=true
# ENABLE_COOKIES=true
# ENABLE_FEEDBACK=true
