{"production": false, "debug": false, "ui": {"ssl": false, "host": "localhost", "port": 4000, "nameSpace": "/", "rateLimiter": {"windowMs": 60000, "max": 500}, "useProxies": true, "baseUrl": "http://localhost:4000/"}, "rest": {"ssl": true, "host": "sandbox.dspace.org", "port": 443, "nameSpace": "/server", "baseUrl": "https://sandbox.dspace.org/server"}, "actuators": {"endpointPath": "/actuator/health"}, "cache": {"msToLive": {"default": 900000}, "control": "max-age=604800", "autoSync": {"defaultTime": 0, "maxBufferSize": 100, "timePerMethod": {"PATCH": 3}}, "serverSide": {"debug": false, "headers": ["Link"], "botCache": {"max": 1000, "timeToLive": 86400000, "allowStale": true}, "anonymousCache": {"max": 0, "timeToLive": 10000, "allowStale": true}}}, "auth": {"ui": {"timeUntilIdle": 900000, "idleGracePeriod": 300000}, "rest": {"timeLeftBeforeTokenRefresh": 120000}}, "form": {"spellCheck": true, "validatorMap": {"required": "required", "regex": "pattern"}}, "notifications": {"rtl": false, "position": ["top", "right"], "maxStack": 8, "timeOut": 5000, "clickToClose": true, "animate": "scale"}, "submission": {"autosave": {"metadata": [], "timer": 0}, "duplicateDetection": {"alwaysShowSection": false}, "typeBind": {"field": "dc.type"}, "icons": {"metadata": [{"name": "dc.author", "style": "fas fa-user"}, {"name": "default", "style": ""}], "authority": {"confidence": [{"value": 600, "style": "text-success", "icon": "fa-circle-check"}, {"value": 500, "style": "text-info", "icon": "fa-gear"}, {"value": 400, "style": "text-warning", "icon": "fa-circle-question"}, {"value": 300, "style": "text-muted", "icon": "fa-circle-question"}, {"value": 200, "style": "text-muted", "icon": "fa-circle-exclamation"}, {"value": 100, "style": "text-muted", "icon": "fa-circle-stop"}, {"value": 0, "style": "text-muted", "icon": "fa-ban"}, {"value": -1, "style": "text-muted", "icon": "fa-circle-xmark"}, {"value": "default", "style": "text-muted", "icon": "fa-circle-xmark"}]}}}, "defaultLanguage": "en", "languages": [{"code": "en", "label": "English", "active": true}, {"code": "ar", "label": "العربية", "active": true}, {"code": "bn", "label": "বাংলা", "active": true}, {"code": "ca", "label": "Català", "active": true}, {"code": "cs", "label": "Čeština", "active": true}, {"code": "de", "label": "De<PERSON>ch", "active": true}, {"code": "el", "label": "Ελληνικά", "active": true}, {"code": "es", "label": "Español", "active": true}, {"code": "fi", "label": "<PERSON><PERSON>", "active": true}, {"code": "fr", "label": "Français", "active": true}, {"code": "gd", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": true}, {"code": "hi", "label": "हिंदी", "active": true}, {"code": "hu", "label": "<PERSON><PERSON><PERSON>", "active": true}, {"code": "it", "label": "Italiano", "active": true}, {"code": "kk", "label": "Қазақ", "active": true}, {"code": "lv", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "active": true}, {"code": "nl", "label": "Nederlands", "active": true}, {"code": "pl", "label": "<PERSON><PERSON>", "active": true}, {"code": "pt-PT", "label": "Português", "active": true}, {"code": "pt-BR", "label": "Português do Brasil", "active": true}, {"code": "sr-lat", "label": "Srps<PERSON> (lat)", "active": true}, {"code": "sr-cyr", "label": "Српски", "active": true}, {"code": "sv", "label": "Svenska", "active": true}, {"code": "tr", "label": "Türkçe", "active": true}, {"code": "uk", "label": "Yкраї́нська", "active": true}, {"code": "vi", "label": "Tiếng <PERSON>", "active": true}], "browseBy": {"oneYearLimit": 10, "fiveYearLimit": 30, "defaultLowerLimit": 1900, "showThumbnails": true, "pageSize": 20}, "communityList": {"pageSize": 20}, "homePage": {"recentSubmissions": {"pageSize": 5, "sortField": "dc.date.accessioned"}, "topLevelCommunityList": {"pageSize": 5}, "showDiscoverFilters": false}, "item": {"edit": {"undoTimeout": 10000}, "showAccessStatuses": false, "bitstream": {"pageSize": 5}}, "community": {"searchSection": {"showSidebar": true}}, "collection": {"searchSection": {"showSidebar": true}, "edit": {"undoTimeout": 10000}}, "suggestion": [], "themes": [{"name": "custom", "headTags": [{"tagName": "link", "attributes": {"rel": "icon", "href": "assets/dspace/images/favicons/favicon.ico", "sizes": "any"}}, {"tagName": "link", "attributes": {"rel": "icon", "href": "assets/dspace/images/favicons/favicon.svg", "type": "image/svg+xml"}}, {"tagName": "link", "attributes": {"rel": "apple-touch-icon", "href": "assets/dspace/images/favicons/apple-touch-icon.png"}}, {"tagName": "link", "attributes": {"rel": "manifest", "href": "assets/dspace/images/favicons/manifest.webmanifest"}}]}], "bundle": {"standardBundles": ["ORIGINAL", "THUMBNAIL", "LICENSE"]}, "mediaViewer": {"image": false, "video": false}, "info": {"enableEndUserAgreement": true, "enableAboutStatement": true, "enableCreditsStatement": true, "enableHelpStatement": true, "enablePrivacyStatement": true, "enableCOARNotifySupport": true}, "markdown": {"enabled": false, "mathjax": false}, "vocabularies": [{"filter": "subject", "vocabulary": "srsc", "enabled": false}], "comcolSelectionSort": {"sortField": "dc.title", "sortDirection": "ASC"}, "qualityAssuranceConfig": {"sourceUrlMapForProjectSearch": {"openaire": "https://explore.openaire.eu/search/project?projectId="}, "pageSize": 5}, "search": {"advancedFilters": {"enabled": false, "filter": ["title", "author", "subject", "entityType"]}, "filterPlaceholdersCount": 5}, "notifyMetrics": [{"title": "admin-notify-dashboard.received-ldn", "boxes": [{"color": "#B8DAFF", "title": "admin-notify-dashboard.NOTIFY.incoming.accepted", "config": "NOTIFY.incoming.accepted", "description": "admin-notify-dashboard.NOTIFY.incoming.accepted.description"}, {"color": "#D4EDDA", "title": "admin-notify-dashboard.NOTIFY.incoming.processed", "config": "NOTIFY.incoming.processed", "description": "admin-notify-dashboard.NOTIFY.incoming.processed.description"}, {"color": "#FDBBC7", "title": "admin-notify-dashboard.NOTIFY.incoming.failure", "config": "NOTIFY.incoming.failure", "description": "admin-notify-dashboard.NOTIFY.incoming.failure.description"}, {"color": "#FDBBC7", "title": "admin-notify-dashboard.NOTIFY.incoming.untrusted", "config": "NOTIFY.incoming.untrusted", "description": "admin-notify-dashboard.NOTIFY.incoming.untrusted.description"}, {"color": "#43515F", "title": "admin-notify-dashboard.NOTIFY.incoming.involvedItems", "textColor": "#fff", "config": "NOTIFY.incoming.involvedItems", "description": "admin-notify-dashboard.NOTIFY.incoming.involvedItems.description"}]}, {"title": "admin-notify-dashboard.generated-ldn", "boxes": [{"color": "#D4EDDA", "title": "admin-notify-dashboard.NOTIFY.outgoing.delivered", "config": "NOTIFY.outgoing.delivered", "description": "admin-notify-dashboard.NOTIFY.outgoing.delivered.description"}, {"color": "#B8DAFF", "title": "admin-notify-dashboard.NOTIFY.outgoing.queued", "config": "NOTIFY.outgoing.queued", "description": "admin-notify-dashboard.NOTIFY.outgoing.queued.description"}, {"color": "#FDEEBB", "title": "admin-notify-dashboard.NOTIFY.outgoing.queued_for_retry", "config": "NOTIFY.outgoing.queued_for_retry", "description": "admin-notify-dashboard.NOTIFY.outgoing.queued_for_retry.description"}, {"color": "#FDBBC7", "title": "admin-notify-dashboard.NOTIFY.outgoing.failure", "config": "NOTIFY.outgoing.failure", "description": "admin-notify-dashboard.NOTIFY.outgoing.failure.description"}, {"color": "#43515F", "title": "admin-notify-dashboard.NOTIFY.outgoing.involvedItems", "textColor": "#fff", "config": "NOTIFY.outgoing.involvedItems", "description": "admin-notify-dashboard.NOTIFY.outgoing.involvedItems.description"}]}], "liveRegion": {"messageTimeOutDurationMs": 30000, "isVisible": false}}