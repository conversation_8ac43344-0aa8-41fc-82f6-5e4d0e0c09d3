#!/bin/bash

# Tholkudi Digital Archive Deployment Script
# Usage: ./deploy.sh [dev|prod|stop|logs|status]

set -e

PROJECT_NAME="tholkudi-digital-archive"
DEV_COMPOSE="docker-compose.tholkudi.yml"
PROD_COMPOSE="docker-compose.tholkudi.prod.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install it and try again."
        exit 1
    fi
}

# Development deployment
deploy_dev() {
    print_status "Starting Tholkudi Digital Archive in development mode..."
    check_docker
    check_docker_compose
    
    if [ ! -f "$DEV_COMPOSE" ]; then
        print_error "Development compose file not found: $DEV_COMPOSE"
        exit 1
    fi
    
    docker-compose -f "$DEV_COMPOSE" down --remove-orphans
    docker-compose -f "$DEV_COMPOSE" up --build -d
    
    print_success "Development environment started!"
    print_status "Application will be available at: http://localhost:4000"
    print_status "Live reload is enabled for development"
    print_status "Use './deploy.sh logs' to view logs"
}

# Production deployment
deploy_prod() {
    print_status "Starting Tholkudi Digital Archive in production mode..."
    check_docker
    check_docker_compose
    
    if [ ! -f "$PROD_COMPOSE" ]; then
        print_error "Production compose file not found: $PROD_COMPOSE"
        exit 1
    fi
    
    print_warning "Make sure to configure DSPACE_REST_HOST in $PROD_COMPOSE"
    read -p "Continue with production deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled."
        exit 0
    fi
    
    docker-compose -f "$PROD_COMPOSE" down --remove-orphans
    docker-compose -f "$PROD_COMPOSE" up --build -d
    
    print_success "Production environment started!"
    print_status "Application is available at: http://localhost:4000"
    print_status "Use './deploy.sh logs' to view logs"
}

# Stop all services
stop_services() {
    print_status "Stopping Tholkudi Digital Archive services..."
    
    if [ -f "$DEV_COMPOSE" ]; then
        docker-compose -f "$DEV_COMPOSE" down --remove-orphans
    fi
    
    if [ -f "$PROD_COMPOSE" ]; then
        docker-compose -f "$PROD_COMPOSE" down --remove-orphans
    fi
    
    print_success "All services stopped."
}

# Show logs
show_logs() {
    print_status "Showing logs for Tholkudi Digital Archive..."
    
    if docker ps --format "table {{.Names}}" | grep -q "$PROJECT_NAME"; then
        if docker ps --format "table {{.Names}}" | grep -q "$PROJECT_NAME-prod"; then
            docker-compose -f "$PROD_COMPOSE" logs -f
        else
            docker-compose -f "$DEV_COMPOSE" logs -f
        fi
    else
        print_warning "No running containers found for $PROJECT_NAME"
    fi
}

# Show status
show_status() {
    print_status "Checking status of Tholkudi Digital Archive..."
    
    echo -e "\n${BLUE}Docker Containers:${NC}"
    docker ps --filter "name=$PROJECT_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo -e "\n${BLUE}Container Health:${NC}"
    if docker ps --format "{{.Names}}" | grep -q "$PROJECT_NAME"; then
        for container in $(docker ps --format "{{.Names}}" | grep "$PROJECT_NAME"); do
            health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no healthcheck")
            echo "$container: $health"
        done
    else
        echo "No containers running"
    fi
    
    echo -e "\n${BLUE}Application URLs:${NC}"
    echo "Development: http://localhost:4000"
    echo "Production:  http://localhost:4000"
}

# Show help
show_help() {
    echo "Tholkudi Digital Archive Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev     Start in development mode (with live reload)"
    echo "  prod    Start in production mode (optimized build)"
    echo "  stop    Stop all services"
    echo "  logs    Show application logs"
    echo "  status  Show service status"
    echo "  help    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev          # Start development environment"
    echo "  $0 prod         # Start production environment"
    echo "  $0 logs         # View logs"
    echo "  $0 stop         # Stop all services"
}

# Main script logic
case "${1:-help}" in
    "dev")
        deploy_dev
        ;;
    "prod")
        deploy_prod
        ;;
    "stop")
        stop_services
        ;;
    "logs")
        show_logs
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac
