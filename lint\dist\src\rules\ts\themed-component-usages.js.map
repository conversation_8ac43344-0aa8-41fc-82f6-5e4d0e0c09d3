{"version": 3, "file": "themed-component-usages.js", "sourceRoot": "", "sources": ["../../../../src/rules/ts/themed-component-usages.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,oDAGkC;AAGlC,mDAAgD;AAChD,wCAGwB;AAExB,4DAMkC;AAClC,sDAM+B;AAE/B,IAAY,OAKX;AALD,WAAY,OAAO;IACjB,oDAAyC,CAAA;IACzC,mDAAwC,CAAA;IACxC,0DAA+C,CAAA;IAC/C,4DAAiD,CAAA;AACnD,CAAC,EALW,OAAO,uBAAP,OAAO,QAKlB;AAEY,QAAA,IAAI,GAAG;IAClB,IAAI,EAAE,yBAAyB;IAC/B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE;;;;;;;;OAQZ;SACF;QACD,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,uEAAuE;YAC9F,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,uEAAuE;YAC/F,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,uEAAuE;YACjG,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,6DAA6D;SACxF;KACF;IACD,cAAc,EAAE,EAAE;CACK,CAAC;AAEb,QAAA,IAAI,GAAG,mBAAW,CAAC,WAAW,CAAC,WAAW,CAAC;IACtD,GAAG,YAAI;IACP,MAAM,CAAC,OAAwC;QAC7C,MAAM,QAAQ,GAAG,IAAA,wBAAW,EAAC,OAAO,CAAC,CAAC;QAEtC,SAAS,gCAAgC,CAAC,IAAyB;YACjE,IAAI,IAAA,sCAAsB,EAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,IAAA,gDAAgC,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1D,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,2BAA2B;gBAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAC7E,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,OAAO,CAAC,WAAW;gBAC9B,IAAI,EAAE,IAAI;gBACV,GAAG,CAAC,KAAK;oBACP,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;wBACjE,OAAO,IAAA,oCAA8B,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;oBAClF,CAAC;yBAAM,CAAC;wBACN,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;oBACrD,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,kCAAkC,CAAC,IAAsB;YAChE,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,OAAO,CAAC,cAAc;gBACjC,GAAG,CAAC,KAAK;oBACP,MAAM,WAAW,GAAG,IAAA,4BAAY,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC3C,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC9C,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,SAAS,iCAAiC,CAAC,aAAuC;YAChF,MAAM,SAAS,GAAG,IAAA,uBAAU,EAAC,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAA,sCAAsB,EAAC,KAAK,CAAC,CAAC,CAAC;YAE5E,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC;YAC5C,MAAM,eAAe,GAAG,aAAa,CAAC,MAAoC,CAAC;YAE3E,MAAM,KAAK,GAAG,IAAA,gDAAgC,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,2BAA2B;gBAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;YACrF,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,OAAO,CAAC,YAAY;gBAC/B,IAAI,EAAE,YAAY;gBAClB,GAAG,CAAC,KAAK;oBACP,MAAM,GAAG,GAAG,EAAE,CAAC;oBAEf,MAAM,aAAa,GAAG,IAAA,gCAAmB,EAAC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;oBAEvE,IAAI,IAAA,6BAAgB,EAAC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC/D,0DAA0D;wBAE1D,MAAM,aAAa,GAAG,YAAY,KAAK,CAAC,YAAY,YAAY,IAAA,yBAAY,EAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;wBAE9G,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC5C,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;gCAC1C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,CAAC;4BAC9D,CAAC;iCAAM,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gCACvC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC;4BACzE,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,GAAG,CAAC,IAAI,CAAC,GAAG,IAAA,sBAAgB,EAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;4BAC7D,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gCAChC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC;4BACzE,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,6DAA6D;wBAE7D,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;4BAC1C,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gCAC5C,0CAA0C;gCAC1C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;4BACxF,CAAC;iCAAM,CAAC;gCACN,GAAG,CAAC,IAAI,CAAC,GAAG,IAAA,sBAAgB,EAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;4BAC/D,CAAC;wBACH,CAAC;oBACH,CAAC;oBAED,OAAO,GAAG,CAAC;gBACb,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,uCAAuC;QACvC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,OAAO;gBACL,CAAC,4GAA4G,0CAA0B,MAAM,CAAC,EAAE,kCAAkC;aACnL,CAAC;QACJ,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,OAAO;gBACL,CAAC,4GAA4G,0CAA0B,MAAM,CAAC,EAAE,kCAAkC;aACnL,CAAC;QACJ,CAAC;aAAM,IACL,QAAQ,CAAC,KAAK,CAAC,2CAA2C,CAAC;eACxD,QAAQ,CAAC,KAAK,CAAC,2BAA2B,CAAC,EAC9C,CAAC;YACD,aAAa;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,OAAO,IAAA,sCAAsB,GAAE,CAAC,MAAM,CACpC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACf,OAAO;oBACL,GAAG,KAAK;oBACR,CAAC,0EAA0E,KAAK,CAAC,SAAS,IAAI,CAAC,EAAE,gCAAgC;oBACjI,CAAC,oCAAoC,KAAK,CAAC,SAAS,IAAI,CAAC,EAAE,iCAAiC;iBAC7F,CAAC;YACJ,CAAC,EAAE,EAAE,CACN,CAAC;QACJ,CAAC;IAEH,CAAC;CACF,CAAC,CAAC;AAEU,QAAA,KAAK,GAAG;IACnB,MAAM,EAAE,YAAI,CAAC,IAAI;IACjB,KAAK,EAAE;QACL;YACE,IAAI,EAAE,4BAA4B;YAClC,IAAI,EAAE;;;;;;;SAOH;SACJ;QACD;YACE,IAAI,EAAE,uCAAuC;YAC7C,IAAI,EAAE;;;SAGH;SACJ;QACD;YACE,IAAI,EAAE,kCAAkC;YACxC,IAAI,EAAE;;;;;SAKH;SACJ;QACD;YACE,IAAI,EAAE,+BAA+B;YACrC,IAAI,EAAE;;;;;;SAMH;SACJ;QACD;YACE,IAAI,EAAE,yCAAyC;YAC/C,QAAQ,EAAE,IAAA,iBAAO,EAAC,qCAAqC,CAAC;YACxD,IAAI,EAAE;;;SAGH;SACJ;QACD;YACE,IAAI,EAAE,4CAA4C;YAClD,QAAQ,EAAE,IAAA,iBAAO,EAAC,mCAAmC,CAAC;YACtD,IAAI,EAAE;;;SAGH;SACJ;KACF;IACD,OAAO,EAAE;QACP;YACE,IAAI,EAAE,sCAAsC;YAC5C,IAAI,EAAE;;;;;;;;SAQH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,YAAY;iBAChC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,WAAW;iBAC/B;aACF;YACD,MAAM,EAAE;;;;;;;;SAQL;SACJ;QACD;YACE,IAAI,EAAE,0DAA0D;YAChE,IAAI,EAAE;;;;;;;;;SASH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,YAAY;iBAChC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,WAAW;iBAC/B;aACF;YACD,MAAM,EAAE;;;;;;;;;;SAUL;SACJ;QACD;YACE,IAAI,EAAE,qCAAqC;YAC3C,IAAI,EAAE;;;;;;;SAOH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,WAAW;iBAC/B;aACF;YACD,MAAM,EAAE;;;;;;SAML;SACJ;QACD;YACE,IAAI,EAAE,4CAA4C;YAClD,QAAQ,EAAE,IAAA,iBAAO,EAAC,qCAAqC,CAAC;YACxD,IAAI,EAAE;;;SAGH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;aACF;YACD,MAAM,EAAE;;;SAGL;SACJ;QACD;YACE,IAAI,EAAE,wCAAwC;YAC9C,QAAQ,EAAE,IAAA,iBAAO,EAAC,qCAAqC,CAAC;YACxD,IAAI,EAAE;;;SAGH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;aACF;YACD,MAAM,EAAE;;;SAGL;SACJ;QACD;YACE,IAAI,EAAE,+CAA+C;YACrD,QAAQ,EAAE,IAAA,iBAAO,EAAC,mCAAmC,CAAC;YACtD,IAAI,EAAE;;;SAGH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;aACF;YACD,MAAM,EAAE;;;SAGL;SACJ;QACD;YACE,IAAI,EAAE,2CAA2C;YACjD,QAAQ,EAAE,IAAA,iBAAO,EAAC,mCAAmC,CAAC;YACtD,IAAI,EAAE;;;SAGH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,cAAc;iBAClC;aACF;YACD,MAAM,EAAE;;;SAGL;SACJ;QACD;YACE,IAAI,EAAE,iGAAiG;YACvG,QAAQ,EAAE,IAAA,iBAAO,EAAC,uDAAuD,CAAC;YAC1E,IAAI,EAAE;;;;;;;;;;;;OAYL;YACD,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,YAAY;iBAChC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,WAAW;iBAC/B;aACF;YACD,MAAM,EAAE;;;;;;;;;;;;OAYP;SACF;QACD;YACE,IAAI,EAAE,kEAAkE;YACxE,QAAQ,EAAE,IAAA,iBAAO,EAAC,uDAAuD,CAAC;YAC1E,IAAI,EAAE;;;;;;;;;;;;;OAaL;YACD,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,YAAY;iBAChC;gBACD;oBACE,SAAS,EAAE,OAAO,CAAC,WAAW;iBAC/B;aACF;YACD,MAAM,EAAE;;;;;;;;;;;;OAYP;SACF;KACF;CACF,CAAC;AAEF,kBAAe,YAAI,CAAC"}