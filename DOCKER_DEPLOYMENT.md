# 🐳 Docker Deployment Guide for Tholkudi Digital Archive

This guide provides comprehensive instructions for deploying the Tholkudi Digital Archive using Docker containers.

## Prerequisites

- Docker Engine 20.10+ installed
- Docker Compose 2.0+ installed
- At least 4GB RAM available
- DSpace REST API backend running (local or remote)

## Quick Start

### 1. Development Mode (Recommended for Testing)

```bash
# Clone and navigate to project
cd tholkudi

# Start development container with live reload
docker-compose -f docker-compose.tholkudi.yml up --build

# Access the application
open http://localhost:4000
```

### 2. Production Mode

```bash
# Build and start production container
docker-compose -f docker-compose.tholkudi.prod.yml up --build -d

# Check container status
docker-compose -f docker-compose.tholkudi.prod.yml ps

# View logs
docker-compose -f docker-compose.tholkudi.prod.yml logs -f
```

## Configuration

### Environment Variables

Edit the docker-compose files to configure:

| Variable | Description | Default |
|----------|-------------|---------|
| `DSPACE_UI_HOST` | UI host binding | 0.0.0.0 |
| `DSPACE_UI_PORT` | UI port | 4000 |
| `DSPACE_REST_HOST` | DSpace REST API host | localhost |
| `DSPACE_REST_PORT` | DSpace REST API port | 8080 |
| `DSPACE_REST_SSL` | Use HTTPS for REST API | false |
| `NODE_ENV` | Node environment | development/production |

### Backend Configuration

**For Development:**
```yaml
DSPACE_REST_SSL: 'false'
DSPACE_REST_HOST: localhost
DSPACE_REST_PORT: 8080
```

**For Production:**
```yaml
DSPACE_REST_SSL: 'true'
DSPACE_REST_HOST: your-dspace-backend.com
DSPACE_REST_PORT: 443
```

## Deployment Options

### Option 1: Using Existing Dockerfiles

```bash
# Development
docker-compose -f docker/docker-compose.yml up --build

# Production
docker-compose -f docker/docker-compose-dist.yml up --build
```

### Option 2: Using Custom Tholkudi Configuration

```bash
# Development with Tholkudi customizations
docker-compose -f docker-compose.tholkudi.yml up --build

# Production with optimizations
docker-compose -f docker-compose.tholkudi.prod.yml up --build
```

### Option 3: Custom Dockerfile

```bash
# Build with custom Dockerfile
docker build -f Dockerfile.tholkudi -t tholkudi-archive:latest .

# Run container
docker run -d \
  --name tholkudi-archive \
  -p 4000:4000 \
  -e DSPACE_REST_HOST=your-backend.com \
  tholkudi-archive:latest
```

## Production Deployment

### 1. Build Production Image

```bash
# Build optimized production image
docker build -f Dockerfile.tholkudi -t tholkudi-archive:prod .
```

### 2. Run with Docker Compose

```bash
# Start production services
docker-compose -f docker-compose.tholkudi.prod.yml up -d

# Scale if needed
docker-compose -f docker-compose.tholkudi.prod.yml up -d --scale tholkudi-ui=3
```

### 3. Behind Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check container health
docker inspect --format='{{.State.Health.Status}}' tholkudi-digital-archive

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' tholkudi-digital-archive
```

### Logs

```bash
# View real-time logs
docker-compose -f docker-compose.tholkudi.prod.yml logs -f

# View specific service logs
docker logs tholkudi-digital-archive
```

### Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.tholkudi.prod.yml up --build -d

# Clean up old images
docker image prune -f
```

## Troubleshooting

### Common Issues

1. **Out of Memory**
   ```bash
   # Increase memory limit
   docker run --memory=4g tholkudi-archive:latest
   ```

2. **Port Already in Use**
   ```bash
   # Use different port
   docker run -p 3000:4000 tholkudi-archive:latest
   ```

3. **Backend Connection Issues**
   ```bash
   # Check network connectivity
   docker exec -it tholkudi-digital-archive curl -I http://your-backend:8080/server
   ```

### Performance Optimization

1. **Enable BuildKit**
   ```bash
   export DOCKER_BUILDKIT=1
   docker build -f Dockerfile.tholkudi -t tholkudi-archive:latest .
   ```

2. **Multi-stage Caching**
   ```bash
   # Build with cache mount
   docker build --cache-from tholkudi-archive:latest -t tholkudi-archive:latest .
   ```

## Security Considerations

1. **Run as non-root user** (already configured in Dockerfile.tholkudi)
2. **Use secrets for sensitive data**
3. **Enable HTTPS in production**
4. **Regular security updates**

```bash
# Update base images
docker pull node:18-alpine
docker-compose -f docker-compose.tholkudi.prod.yml build --no-cache
```

## Backup and Recovery

```bash
# Backup configuration
docker cp tholkudi-digital-archive:/app/config ./backup/

# Export container
docker export tholkudi-digital-archive > tholkudi-backup.tar
```

For more detailed configuration options, see the [DSpace Angular Documentation](https://github.com/DSpace/dspace-angular).
