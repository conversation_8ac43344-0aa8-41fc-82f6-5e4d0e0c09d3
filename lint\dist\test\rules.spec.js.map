{"version": 3, "file": "rules.spec.js", "sourceRoot": "", "sources": ["../../test/rules.spec.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;AAEH,6DAA0D;AAC1D,yDAAsD;AACtD,uCAGmB;AAEnB,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,YAAQ,CAAC,KAAK,EAAE,CAAC;QACnD,sBAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAY,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,cAAU,CAAC,KAAK,EAAE,CAAC;QACrD,wBAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC,CAAC,CAAC"}