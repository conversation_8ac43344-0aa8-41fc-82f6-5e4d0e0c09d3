#
# The contents of this file are subject to the license and copyright
# detailed in the LICENSE and NOTICE files at the root of the source
# tree and available online at
#
# http://www.dspace.org/license/
#

#
# This is a copy of the cli.ingest.yml that is available in the DSpace/DSpace
# (Backend) at:
# https://github.com/DSpace/DSpace/blob/main/dspace/src/main/docker-compose/cli.ingest.yml
#
# Therefore, it should be kept in sync with that file
services:
  dspace-cli:
    environment:
    - AIPZIP=https://github.com/DSpace-Labs/AIP-Files/raw/main/dogAndReport.zip
    - ADMIN_EMAIL=<EMAIL>
    - AIPDIR=/tmp/aip-dir
    entrypoint:
      - /bin/bash
      - '-c'
      - |
          rm -rf $${AIPDIR}
          mkdir $${AIPDIR} /dspace/upload
          cd $${AIPDIR}
          pwd
          curl $${AIPZIP} -L -s --output aip.zip
          unzip aip.zip
          cd $${AIPDIR}

          /dspace/bin/dspace packager -r -a -t AIP -e $${ADMIN_EMAIL} -f -u SITE*.zip
          /dspace/bin/dspace database update-sequences
          touch /dspace/solr/search/conf/reindex.flag

          /dspace/bin/dspace oai import
          /dspace/bin/dspace oai clean-cache
