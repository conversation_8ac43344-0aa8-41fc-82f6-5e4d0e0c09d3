{"version": 3, "file": "themed-component-selectors.js", "sourceRoot": "", "sources": ["../../../../src/rules/ts/themed-component-selectors.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,oDAGkC;AAGlC,mDAAgD;AAChD,gDAA8D;AAC9D,0CAAgD;AAEhD,4DAIkC;AAClC,sDAAoD;AAEpD,IAAY,OAIX;AAJD,WAAY,OAAO;IACjB,kDAAuC,CAAA;IACvC,0DAA+C,CAAA;IAC/C,0DAA+C,CAAA;AACjD,CAAC,EAJW,OAAO,uBAAP,OAAO,QAIlB;AAEY,QAAA,IAAI,GAAG;IAClB,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE;;;;;;;;;;OAUZ;SACF;QACD,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,2FAA2F;YAC3G,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,yGAAyG;YAC5H,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,2FAA2F;SAC9G;KACF;IACD,cAAc,EAAE,EAAE;CACK,CAAC;AAEb,QAAA,IAAI,GAAG,mBAAW,CAAC,WAAW,CAAC,WAAW,CAAC;IACtD,GAAG,YAAI;IACP,MAAM,CAAC,OAAwC;QAC7C,MAAM,QAAQ,GAAG,IAAA,wBAAW,EAAC,OAAO,CAAC,CAAC;QAEtC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,SAAS,sBAAsB,CAAC,YAAoC;YAClE,IAAI,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjD,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,OAAO,CAAC,OAAO;oBAC1B,IAAI,EAAE,YAAY;oBAClB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,IAAA,oBAAa,EAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;oBACzG,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,mBAAmB,CAAC,YAAoC;YAC/D,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChD,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,OAAO,CAAC,IAAI;oBACvB,IAAI,EAAE,YAAY;oBAClB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,IAAA,oBAAa,EAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;oBACvG,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,YAAoC;YACjE,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClD,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,OAAO,CAAC,MAAM;oBACzB,IAAI,EAAE,YAAY;oBAClB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,IAAA,oBAAa,EAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;oBACzG,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,oEAAoE,CAAC,IAAwB;gBAC3F,MAAM,YAAY,GAAG,IAAA,kCAAwB,EAAC,IAAI,CAAC,CAAC;gBAEpD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;oBAC/B,OAAO;gBACT,CAAC;gBAED,MAAM,QAAQ,GAAG,YAAY,EAAE,KAAK,CAAC;gBACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAmC,CAAC;gBAC3D,MAAM,SAAS,GAAG,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC;gBAErC,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBACtD,OAAO;gBACT,CAAC;gBAED,IAAI,IAAA,wCAAwB,EAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,sBAAsB,CAAC,YAAY,CAAC,CAAC;gBACvC,CAAC;qBAAM,IAAI,IAAA,6CAA6B,EAAC,QAAQ,CAAC,EAAE,CAAC;oBACnD,qBAAqB,CAAC,YAAY,CAAC,CAAC;gBACtC,CAAC;qBAAM,IAAI,IAAA,oCAAoB,EAAC,SAAS,CAAC,EAAE,CAAC;oBAC3C,mBAAmB,CAAC,YAAY,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEU,QAAA,KAAK,GAAG;IACnB,MAAM,EAAE,YAAI,CAAC,IAAI;IACjB,KAAK,EAAE;QACL;YACE,IAAI,EAAE,0CAA0C;YAChD,IAAI,EAAE;;;;;;OAML;SACF;QACD;YACE,IAAI,EAAE,kHAAkH;YACxH,IAAI,EAAE;;;;;;;;;;;;;;;;;;OAkBL;SACF;QACD;YACE,IAAI,EAAE,sDAAsD;YAC5D,IAAI,EAAE;;;;;;;;;;;;OAYL;SACF;KACF;IACD,OAAO,EAAE;QACP;YACE,IAAI,EAAE,mCAAmC;YACzC,QAAQ,EAAE,IAAA,iBAAO,EAAC,0CAA0C,CAAC;YAC7D,IAAI,EAAE;;;;;;SAMH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,IAAI;iBACxB;aACF;YACD,MAAM,EAAE;;;;;;SAML;SACJ;QACD;YACE,IAAI,EAAE,sCAAsC;YAC5C,QAAQ,EAAE,IAAA,iBAAO,EAAC,iDAAiD,CAAC;YACpE,IAAI,EAAE;;;;;;SAMH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,OAAO;iBAC3B;aACF;YACD,MAAM,EAAE;;;;;;SAML;SACJ;QACD;YACE,IAAI,EAAE,mCAAmC;YACzC,QAAQ,EAAE,IAAA,iBAAO,EAAC,sDAAsD,CAAC;YACzE,IAAI,EAAE;;;;;;SAMH;YACH,MAAM,EAAE;gBACN;oBACE,SAAS,EAAE,OAAO,CAAC,MAAM;iBAC1B;aACF;YACD,MAAM,EAAE;;;;;;SAML;SACJ;KACF;CACF,CAAC;AAEF,kBAAe,YAAI,CAAC"}