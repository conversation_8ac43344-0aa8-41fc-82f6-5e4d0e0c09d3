#
# The contents of this file are subject to the license and copyright
# detailed in the LICENSE and NOTICE files at the root of the source
# tree and available online at
#
# http://www.dspace.org/license/
#

# Docker Compose for running the DSpace backend for e2e testing in a CI environment
# This is used by our GitHub CI at .github/workflows/build.yml
# It is based heavily on the Backend's Docker Compose:
# https://github.com/DSpace/DSpace/blob/main/docker-compose.yml
networks:
  dspacenet:
services:
  # DSpace (backend) webapp container
  dspace:
    container_name: dspace
    environment:
      # Below syntax may look odd, but it is how to override dspace.cfg settings via env variables.
      # See https://github.com/DSpace/DSpace/blob/main/dspace/config/config-definition.xml
      # __P__ => "." (e.g. dspace__P__dir => dspace.dir)
      # __D__ => "-" (e.g. google__D__metadata => google-metadata)
      # dspace.dir, dspace.server.url and dspace.ui.url
      dspace__P__dir: /dspace
      dspace__P__server__P__url: http://127.0.0.1:8080/server
      dspace__P__ui__P__url: http://127.0.0.1:4000
      # db.url: Ensure we are using the 'dspacedb' image for our database
      db__P__url: '**************************************'
      # solr.server: Ensure we are using the 'dspacesolr' image for Solr
      solr__P__server: http://dspacesolr:8983/solr
      # Tell Statistics to commit all views immediately instead of waiting on Solr's autocommit.
      # This allows us to generate statistics in e2e tests so that statistics pages can be tested thoroughly.
      solr__D__statistics__P__autoCommit: 'false'
      LOGGING_CONFIG: /dspace/config/log4j2-container.xml
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace:${DSPACE_VER:-dspace-8_x-test}"
    depends_on:
    - dspacedb
    networks:
      - dspacenet
    ports:
    - published: 8080
      target: 8080
    stdin_open: true
    tty: true
    volumes:
    - assetstore:/dspace/assetstore
    # Ensure that the database is ready BEFORE starting tomcat
    # 1. While a TCP connection to dspacedb port 5432 is not available, continue to sleep
    # 2. Then, run database migration to init database tables (including any out-of-order ignored migrations, if any)
    # 3. Finally, start DSpace
    entrypoint:
    - /bin/bash
    - '-c'
    - |
      while (!</dev/tcp/dspacedb/5432) > /dev/null 2>&1; do sleep 1; done;
      /dspace/bin/dspace database migrate ignored
      java -jar /dspace/webapps/server-boot.jar --dspace.dir=/dspace
  # DSpace database container
  # NOTE: This is customized to use our loadsql image, so that we are using a database with existing test data
  dspacedb:
    container_name: dspacedb
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace-postgres-pgcrypto:${DSPACE_VER:-dspace-8_x}-loadsql"
    environment:
      # This LOADSQL should be kept in sync with the LOADSQL in
      # https://github.com/DSpace/DSpace/blob/main/dspace/src/main/docker-compose/db.entities.yml
      # This SQL is available from https://github.com/DSpace-Labs/AIP-Files/releases/tag/demo-entities-data
      # NOTE: currently there is no dspace8 version
      LOADSQL: https://github.com/DSpace-Labs/AIP-Files/releases/download/demo-entities-data/dspace7-entities-data.sql
      PGDATA: /pgdata
      POSTGRES_PASSWORD: dspace
    networks:
      - dspacenet
    ports:
    - published: 5432
      target: 5432
    stdin_open: true
    tty: true
    volumes:
     # Keep Postgres data directory between reboots
    - pgdata:/pgdata
  # DSpace Solr container
  dspacesolr:
    container_name: dspacesolr
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace-solr:${DSPACE_VER:-dspace-8_x}"
    networks:
      - dspacenet
    ports:
    - published: 8983
      target: 8983
    stdin_open: true
    tty: true
    working_dir: /var/solr/data
    volumes:
    # Keep Solr data directory between reboots
    - solr_data:/var/solr/data
    # Initialize all DSpace Solr cores using the mounted configsets (see above), then start Solr
    entrypoint:
    - /bin/bash
    - '-c'
    - |
      init-var-solr
      precreate-core authority /opt/solr/server/solr/configsets/authority
      cp -r /opt/solr/server/solr/configsets/authority/* authority
      precreate-core oai /opt/solr/server/solr/configsets/oai
      cp -r /opt/solr/server/solr/configsets/oai/* oai
      precreate-core search /opt/solr/server/solr/configsets/search
      cp -r /opt/solr/server/solr/configsets/search/* search
      precreate-core statistics /opt/solr/server/solr/configsets/statistics
      cp -r /opt/solr/server/solr/configsets/statistics/* statistics
      precreate-core qaevent /opt/solr/server/solr/configsets/qaevent
      cp -r /opt/solr/server/solr/configsets/qaevent/* qaevent
      precreate-core suggestion /opt/solr/server/solr/configsets/suggestion
      cp -r /opt/solr/server/solr/configsets/suggestion/* suggestion
      exec solr -f
volumes:
  assetstore:
  pgdata:
  solr_data:
