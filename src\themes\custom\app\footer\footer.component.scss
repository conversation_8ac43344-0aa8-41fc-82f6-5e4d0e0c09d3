:host {
  footer {
    background-color: #f1f1e9; // Updated footer background color
    color: #333;
    z-index: var(--ds-footer-z-index);
    border-top: 1px solid #ddd;
    padding: 1rem 0 0.5rem 0; // Reduced from 2rem 0 1rem 0 to 1rem 0 0.5rem 0
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.1); // Add shadow to top of footer

    .container {
      max-width: 1200px;
    }

    // Logo column styling
    .logo-column {
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-placeholder {
        width: 80px;
        height: 60px;
        background-color: #ddd;
        border: 2px dashed #999;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        color: #666;
        text-align: center;
        border-radius: 4px;
      }
    }

    // Column headings
    h5 {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.5rem; // Reduced from 1rem to 0.5rem
      color: #8B4513;
      text-align: left;

      // Style for the first word with brown underline
      .first-word {
        border-bottom: 3px solid #8B4513 !important; // <PERSON> underline with !important
        padding-bottom: 3px !important; // Small space between text and underline
        display: inline !important; // Keep it inline with the rest of the text
        text-decoration: underline !important; // Fallback underline
        text-decoration-color: #8B4513 !important; // Brown underline color
        text-underline-offset: 3px !important; // Space between text and underline
      }
    }

    // Content styling
    address,
    ul,
    li,
    p {
      font-size: 0.9rem;
      line-height: 1.4; // Reduced from 1.6 to 1.4 for tighter spacing
      color: #333;
      margin-bottom: 0.2rem; // Reduced from 0.3rem to 0.2rem
    }

    address {
      font-style: normal;
      margin-bottom: 0;
    }

    // Links styling
    a {
      color: #333;
      text-decoration: none;
      transition: color 0.2s ease;

      &:hover,
      &:focus {
        color: #8B4513;
        text-decoration: none;
      }

      &:focus-visible {
        outline: 2px solid #8B4513;
        outline-offset: 2px;
      }
    }

    // Lists
    ul {
      padding-left: 0;
      list-style: none;
      margin-bottom: 0;

      li {
        margin-bottom: 0.2rem; // Reduced from 0.4rem to 0.2rem

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    // Social icons styling - updated for PNG images
    .social-icons {
      display: flex;
      gap: 0.8rem;
      margin-top: 0.5rem;

      a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.7;
        }

        img {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }
    }

    // Bottom copyright section
    .bottom-footer {
      background-color: #f1f1e9; // Updated footer background color
      color: #666;
      font-size: 0.85rem;
      padding: 0.5rem 0 0.3rem 0; // Reduced from 1rem 0 0.5rem 0 to 0.5rem 0 0.3rem 0
      text-align: center;
      border-top: 1px solid #ddd;
      margin-top: 0.8rem; // Reduced from 1.5rem to 0.8rem
    }

    // Responsive adjustments
    @media (max-width: 767.98px) {
      padding: 1rem 0 0.5rem 0; // Reduced from 1.5rem 0 1rem 0 to 1rem 0 0.5rem 0

      .logo-column {
        margin-bottom: 1rem; // Reduced from 1.5rem to 1rem
      }

      h5 {
        text-align: center;
        margin-bottom: 0.5rem; // Reduced from 0.8rem to 0.5rem
      }

      address,
      ul,
      li,
      p {
        text-align: center;
      }

      .social-icons {
        justify-content: center;
      }
    }
  }
}

// Global override to ensure first-word styling is applied
footer h5 .first-word {
  border-bottom: 3px solid #8B4513 !important;
  padding-bottom: 3px !important;
  display: inline !important;
  text-decoration: underline !important;
  text-decoration-color: #8B4513 !important;
  text-underline-offset: 3px !important;
}