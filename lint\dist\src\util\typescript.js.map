{"version": 3, "file": "typescript.js", "sourceRoot": "", "sources": ["../../../src/util/typescript.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,oDAAoD;AAMpD,iCAGgB;AAIhB;;;GAGG;AACH,SAAgB,WAAW,CAAC,OAAuB;IACjD,4IAA4I;IAC5I,mDAAmD;IACnD,OAAO,IAAA,sBAAe,EAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;AAChD,CAAC;AAJD,kCAIC;AAED,SAAgB,aAAa,CAAC,OAAuB;IACnD,4IAA4I;IAC5I,mDAAmD;IACnD,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC;AACjC,CAAC;AAJD,sCAIC;AAED,SAAgB,2BAA2B,CAAC,UAAqC,EAAE,YAAoB;IACrG,KAAK,MAAM,YAAY,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;QACjD,IACE,YAAY,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,QAAQ;eACnD,CACD,CACE,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,UAAU;mBAC1D,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,YAAY,CAC3C,IAAI,CACH,YAAY,CAAC,GAAG,EAAE,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,OAAO;mBACvD,YAAY,CAAC,GAAG,EAAE,KAAK,KAAK,YAAY,CAC5C,CACF,EACD,CAAC;YACD,OAAO,YAAY,CAAC,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAlBD,kEAkBC;AAED,SAAgB,UAAU,CAAC,OAAuB,EAAE,SAA8B;IAChF,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAEtC,MAAM,MAAM,GAA0B,EAAE,CAAC;IAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,IAAI,CAAC,IAAA,YAAK,EAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACjI,MAAM,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,gFAAgF;YAChF,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,IAA2B,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAhBD,gCAgBC;AAED,SAAgB,gBAAgB,CAAC,OAAuB,EAAE,UAAkB;IAC1E,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAEtC,MAAM,MAAM,GAA0B,EAAE,CAAC;IAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YACrF,MAAM,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,gFAAgF;YAChF,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,IAA2B,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAhBD,4CAgBC;AAED,SAAgB,sBAAsB,CAAC,IAAyB;IAC9D,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC3D,CAAC;AAFD,wDAEC;AAED,SAAgB,wBAAwB,CAAC,IAAyB;IAChE,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACxE,CAAC;AAFD,4DAEC;AAED,SAAS,OAAO,CAAC,IAAY;IAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IAEvD,IAAI,CAAC,EAAE,CAAC;QACN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,uDAAuD,IAAI,EAAE,CAAC,CAAC;IACjF,CAAC;AACH,CAAC;AAGD,SAAgB,YAAY,CAAC,QAAgB,EAAE,UAAkB;IAC/D,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE/C,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAChC,UAAU,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM;QACR,CAAC;IACH,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjE,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC;IAEpD,IAAI,MAAc,CAAC;IACnB,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;QAClB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,MAAM,GAAG,IAAI,CAAC;AACvB,CAAC;AAxBD,oCAwBC;AAGD,SAAgB,mBAAmB,CAAC,OAAuB,EAAE,UAAkB;IAC7E,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAEtC,MAAM,MAAM,GAA0B,EAAE,CAAC;IAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YACrF,MAAM,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,gFAAgF;YAChF,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;gBACxF,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAhBD,kDAgBC"}