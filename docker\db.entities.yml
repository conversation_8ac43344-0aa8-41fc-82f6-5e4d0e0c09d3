#
# The contents of this file are subject to the license and copyright
# detailed in the LICENSE and NOTICE files at the root of the source
# tree and available online at
#
# http://www.dspace.org/license/
#

#
# This is a copy of the db.entities.yml that is available in the DSpace/DSpace
# (Backend) at:
# https://github.com/DSpace/DSpace/blob/main/dspace/src/main/docker-compose/db.entities.yml
#
# # Therefore, it should be kept in sync with that file
services:
  dspacedb:
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace-postgres-pgcrypto:${DSPACE_VER:-dspace-8_x}-loadsql"
    environment:
      # This LOADSQL should be kept in sync with the URL in DSpace/DSpace
      # This SQL is available from https://github.com/DSpace-Labs/AIP-Files/releases/tag/demo-entities-data
      # NOTE: currently there is no dspace8 version
      - LOADSQL=https://github.com/DSpace-Labs/AIP-Files/releases/download/demo-entities-data/dspace7-entities-data.sql
  dspace:
    ### OVERRIDE default 'entrypoint' in 'docker-compose-rest.yml' ####
    # Ensure that the database is ready BEFORE starting tomcat
    # 1. While a TCP connection to dspacedb port 5432 is not available, continue to sleep
    # 2. Then, run database migration to init database tables  (including any out-of-order ignored migrations, if any)
    # 3. (Custom for Entities) enable Entity-specific collection submission mappings in item-submission.xml
    #    This 'sed' command inserts the sample configurations specific to the Entities data set, see:
    #    https://github.com/DSpace/DSpace/blob/main/dspace/config/item-submission.xml#L36-L49
    # 4. Finally, start DSpace
    entrypoint:
      - /bin/bash
      - '-c'
      - |
        while (!</dev/tcp/dspacedb/5432) > /dev/null 2>&1; do sleep 1; done;
        /dspace/bin/dspace database migrate ignored
        java -jar /dspace/webapps/server-boot.jar --dspace.dir=/dspace
