#
# Docker Compose for Tholkudi Digital Archive
# Development mode with live reload
#
version: '3.8'

networks:
  tholkudi-net:
    driver: bridge

services:
  tholkudi-ui:
    container_name: tholkudi-digital-archive
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      # UI Configuration
      DSPACE_UI_SSL: 'false'
      DSPACE_UI_HOST: '0.0.0.0'
      DSPACE_UI_PORT: '4000'
      DSPACE_UI_NAMESPACE: /
      
      # Backend Configuration (adjust these to your DSpace REST API)
      DSPACE_REST_SSL: 'false'
      DSPACE_REST_HOST: localhost
      DSPACE_REST_PORT: 8080
      DSPACE_REST_NAMESPACE: /server
      
      # Node.js Configuration
      NODE_ENV: development
      NODE_OPTIONS: "--max_old_space_size=4096"
      
      # Custom theme configuration
      THEME_NAME: custom
    networks:
      - tholkudi-net
    ports:
      - "4000:4000"  # Main application port
      - "9876:9876"  # Live reload port
    volumes:
      # Mount source code for live reload during development
      - ./src:/app/src
      - ./config:/app/config
      - ./assets:/app/assets
    stdin_open: true
    tty: true
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
