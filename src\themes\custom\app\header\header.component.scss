// Import the Akhand-Bold font
@font-face {
  font-family: 'Akhand-Bold';
  src: url('../../../../assets/fonts/Akhand-Bold-BF642a3156ce54d.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
}

// Custom header styling
header {
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;

  .container {
    padding: 0.5rem 1rem;
  }

  .navbar-brand img.tholkudi-logo {
    height: 50px;
    max-width: 200px;
    object-fit: contain;
  }

  // Navigation buttons styling with new color #2b4878
  .nav-button {
    background-color: #2b4878; // New background color as requested
    color: white !important;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px; // Same border radius as existing
    font-family: 'Akhand-Bold', Arial, sans-serif; // Apply Akhand-Bold font
    font-size: 14px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;

    &:hover, &:focus {
      background-color: #1e3456; // Darker shade for hover
      color: white !important;
      text-decoration: none;
    }

    // Dropdown arrow styling
    .fa-chevron-down {
      font-size: 12px;
      margin-left: 8px;
    }
  }

  // User controls wrapper styling with #229daf background
  .user-controls-wrapper {
    background-color: #229daf; // New background color as requested
    border-radius: 20px; // Same border radius as navigation buttons
    padding: 4px 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    // Ensure all components are visible
    ds-auth-nav-menu,
    ds-context-help-toggle,
    ds-lang-switch,
    ds-impersonate-navbar {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      position: relative !important;
      z-index: 1 !important;
    }

    // Style the login text to have white background (now first position)
    ::ng-deep ds-auth-nav-menu {
      .navbar-nav {
        display: flex !important;
        margin: 0 !important;
      }

      .nav-item {
        display: block !important;
      }

      .btn, .dropdown-toggle, a, .dropdownLogin, .dropdownLogout {
        background-color: white !important;
        color: #333 !important;
        border-radius: 16px !important; // Slightly smaller radius for inner element
        padding: 4px 12px !important;
        border: none !important;
        font-family: 'Akhand-Bold', Arial, sans-serif !important; // Apply Akhand-Bold font
        font-size: 14px !important;
        font-weight: bold !important;
        display: inline-block !important;
        text-decoration: none !important;

        // No hover styles as requested
        &:hover, &:focus {
          background-color: white !important;
          color: #333 !important;
          text-decoration: none !important;
        }
      }
    }

    // Style language switcher to blend with the background (now last position)
    ::ng-deep ds-lang-switch {
      .navbar-nav {
        display: flex !important;
        margin: 0 !important;
      }

      .btn, .dropdown-toggle, a {
        background-color: transparent !important;
        color: white !important;
        border: none !important;
        font-family: 'Akhand-Bold', Arial, sans-serif !important; // Apply Akhand-Bold font
        font-size: 14px !important;
        font-weight: bold !important;
        display: inline-block !important;
        text-decoration: none !important;

        // No hover styles as requested
        &:hover, &:focus {
          background-color: transparent !important;
          color: white !important;
          text-decoration: none !important;
        }
      }

      // Style the globe icon specifically
      i.fas.fa-globe-asia {
        color: white !important;
        font-size: 16px !important;
      }
    }

    // Style context help toggle
    ::ng-deep ds-context-help-toggle {
      .btn, button, a {
        background-color: transparent !important;
        color: white !important;
        border: none !important;
        font-size: 16px !important;
        display: inline-block !important;

        &:hover, &:focus {
          background-color: transparent !important;
          color: white !important;
        }
      }
    }
  }

  // Dropdown menu styling
  .dropdown-menu {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 8px 0;

    .dropdown-item {
      padding: 8px 16px;
      font-family: 'Akhand-Bold', Arial, sans-serif !important; // Apply Akhand-Bold font
      font-weight: bold !important;
      font-size: 14px;
      color: #333;

      &:hover, &:focus {
        background-color: #f8f9fa;
        color: #2b4878; // Updated to match new button color
      }
    }
  }

  // Special styling for browse category dropdowns (if they exist)
  .dropdown-menu[class*="browse"] .dropdown-item,
  .dropdown-menu .dropdown-item[href*="browse"] {
    font-family: 'Akhand-Bold', Arial, sans-serif !important;
    font-weight: bold !important;
    color: #2b4878 !important;

    &:hover, &:focus {
      background-color: #2b4878 !important;
      color: white !important;
    }
  }

  // Additional fixes for component visibility
  ::ng-deep {
    // Force visibility of auth nav menu
    ds-auth-nav-menu {
      .navbar-nav {
        display: flex !important;
      }

      .nav-item {
        display: block !important;
      }

      // Make sure dropdown toggles are visible
      .dropdown-toggle {
        display: inline-block !important;
        visibility: visible !important;
      }
    }

    // Force visibility of language switch
    ds-lang-switch {
      .navbar-nav {
        display: flex !important;
      }

      // Make sure dropdown toggles are visible
      .dropdown-toggle {
        display: inline-block !important;
        visibility: visible !important;
      }
    }

    // Force visibility of context help
    ds-context-help-toggle {
      display: block !important;
      visibility: visible !important;
    }
  }

  // Ensure proper spacing between elements
  .me-2 {
    margin-right: 0.5rem !important;
  }

  .me-3 {
    margin-right: 1rem !important;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .d-flex.align-items-center {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .nav-button {
      font-size: 12px;
      padding: 6px 12px;
    }
  }
}