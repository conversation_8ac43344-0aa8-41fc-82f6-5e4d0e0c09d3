#
# Docker Compose for Tholkudi Digital Archive
# Production mode with optimized build
#
version: '3.8'

networks:
  tholkudi-net:
    driver: bridge

services:
  tholkudi-ui:
    container_name: tholkudi-digital-archive-prod
    build:
      context: .
      dockerfile: Dockerfile.dist
    environment:
      # UI Configuration
      DSPACE_UI_SSL: 'false'
      DSPACE_UI_HOST: '0.0.0.0'
      DSPACE_UI_PORT: '4000'
      DSPACE_UI_NAMESPACE: /
      
      # Backend Configuration (MUST be public HTTPS URL for production)
      # Replace with your actual DSpace REST API URL
      DSPACE_REST_SSL: 'true'
      DSPACE_REST_HOST: your-dspace-backend.com
      DSPACE_REST_PORT: 443
      DSPACE_REST_NAMESPACE: /server
      
      # Node.js Configuration
      NODE_ENV: production
      
      # Custom theme configuration
      THEME_NAME: custom
    networks:
      - tholkudi-net
    ports:
      - "4000:4000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:4000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
