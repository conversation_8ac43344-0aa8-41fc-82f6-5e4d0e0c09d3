#
# The contents of this file are subject to the license and copyright
# detailed in the LICENSE and NOTICE files at the root of the source
# tree and available online at
#
# http://www.dspace.org/license/
#

# Docker Compose for running the DSpace backend for testing/development
# This is based heavily on the docker-compose.yml that is available in the DSpace/DSpace
# (Backend) at:
# https://github.com/DSpace/DSpace/blob/main/docker-compose.yml
networks:
  dspacenet:
    ipam:
      config:
        # Define a custom subnet for our DSpace network, so that we can easily trust requests from host to container.
        # If you customize this value, be sure to customize the 'proxies.trusted.ipranges' env variable below.
        - subnet: **********/16
services:
  # DSpace (backend) webapp container
  dspace:
    container_name: dspace
    environment:
      # Below syntax may look odd, but it is how to override dspace.cfg settings via env variables.
      # See https://github.com/DSpace/DSpace/blob/main/dspace/config/config-definition.xml
      # __P__ => "." (e.g. dspace__P__dir => dspace.dir)
      # __D__ => "-" (e.g. google__D__metadata => google-metadata)
      # dspace.dir, dspace.server.url, dspace.ui.url and dspace.name
      dspace__P__dir: /dspace
      # Uncomment to set a non-default value for dspace.server.url or dspace.ui.url
      # dspace__P__server__P__url: http://localhost:8080/server
      # dspace__P__ui__P__url: http://localhost:4000
      dspace__P__name: 'DSpace Started with Docker Compose'
      # db.url: Ensure we are using the 'dspacedb' image for our database
      db__P__url: '**************************************'
      # solr.server: Ensure we are using the 'dspacesolr' image for Solr
      solr__P__server: http://dspacesolr:8983/solr
      # proxies.trusted.ipranges: This setting is required for a REST API running in Docker to trust requests 
      # from the host machine. This IP range MUST correspond to the 'dspacenet' subnet defined above.
      proxies__P__trusted__P__ipranges: '172.23.0'
      LOGGING_CONFIG: /dspace/config/log4j2-container.xml
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace:${DSPACE_VER:-dspace-8_x-test}"
    depends_on:
    - dspacedb
    networks:
      - dspacenet
    ports:
    - published: 8080
      target: 8080
    stdin_open: true
    tty: true
    volumes:
    # Keep DSpace assetstore directory between reboots
    - assetstore:/dspace/assetstore
    # Ensure that the database is ready BEFORE starting tomcat
    # 1. While a TCP connection to dspacedb port 5432 is not available, continue to sleep
    # 2. Then, run database migration to init database tables
    # 3. Finally, start DSpace
    entrypoint:
    - /bin/bash
    - '-c'
    - |
      while (!</dev/tcp/dspacedb/5432) > /dev/null 2>&1; do sleep 1; done;
      /dspace/bin/dspace database migrate
      java -jar /dspace/webapps/server-boot.jar --dspace.dir=/dspace
  # DSpace database container    
  dspacedb:
    container_name: dspacedb
    # Uses a custom Postgres image with pgcrypto installed
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace-postgres-pgcrypto:${DSPACE_VER:-dspace-8_x}"
    environment:
      PGDATA: /pgdata
      POSTGRES_PASSWORD: dspace
    networks:
      - dspacenet
    ports:
    - published: 5432
      target: 5432
    stdin_open: true
    tty: true
    volumes:
    # Keep Postgres data directory between reboots
    - pgdata:/pgdata
  # DSpace Solr container  
  dspacesolr:
    container_name: dspacesolr
    image: "${DOCKER_REGISTRY:-docker.io}/${DOCKER_OWNER:-dspace}/dspace-solr:${DSPACE_VER:-dspace-8_x}"
    networks:
      - dspacenet
    ports:
    - published: 8983
      target: 8983
    stdin_open: true
    tty: true
    working_dir: /var/solr/data
    volumes:
    # Keep Solr data directory between reboots
    - solr_data:/var/solr/data
    # Initialize all DSpace Solr cores using the mounted local configsets (see above), then start Solr
    # * First, run precreate-core to create the core (if it doesn't yet exist). If exists already, this is a no-op
    # * Second, copy configsets to this core:
    #   Updates to Solr configs require the container to be rebuilt/restarted:
    #   `docker compose -p d7 -f docker/docker-compose.yml -f docker/docker-compose-rest.yml up -d --build dspacesolr`
    entrypoint:
    - /bin/bash
    - '-c'
    - |
      init-var-solr
      precreate-core authority /opt/solr/server/solr/configsets/authority
      cp -r /opt/solr/server/solr/configsets/authority/* authority
      precreate-core oai /opt/solr/server/solr/configsets/oai
      cp -r /opt/solr/server/solr/configsets/oai/* oai
      precreate-core search /opt/solr/server/solr/configsets/search
      cp -r /opt/solr/server/solr/configsets/search/* search
      precreate-core statistics /opt/solr/server/solr/configsets/statistics
      cp -r /opt/solr/server/solr/configsets/statistics/* statistics
      precreate-core qaevent /opt/solr/server/solr/configsets/qaevent
      cp -r /opt/solr/server/solr/configsets/qaevent/* qaevent
      precreate-core suggestion /opt/solr/server/solr/configsets/suggestion
      cp -r /opt/solr/server/solr/configsets/suggestion/* suggestion
      exec solr -f
volumes:
  assetstore:
  pgdata:
  solr_data:
