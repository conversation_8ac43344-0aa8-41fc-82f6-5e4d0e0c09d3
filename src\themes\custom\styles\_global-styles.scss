// Add any global css for the theme here

// imports the base global style
@import '../../../styles/_global-styles.scss';

// Custom overrides for the theme
:root {
  --ds-body-bg: #f8f4ec; // New background color as requested
  --ds-header-bg: #f8f4ec; // Header background color same as body
}

// Override the my-cs class to remove margin-top and margin-bottom
.my-cs {
  margin-top: 0 !important; // Remove the margin-top
  margin-bottom: 0 !important; // Remove the margin-bottom to eliminate space above footer
}

// Remove default margins and padding that create unwanted space
html, body {
  margin: 0 !important;
  padding: 0 !important;
}

// Remove spacing between header and content
.outer-wrapper, .inner-wrapper {
  margin: 0 !important;
  padding: 0 !important;
}

// Remove any spacing from main content area
main#main-content {
  margin: 0 !important;
  padding: 0 !important;
}

// Remove spacing from header components
ds-header-navbar-wrapper, ds-breadcrumbs, ds-navbar {
  margin: 0 !important;
  padding: 0 !important;
}

// Completely hide and remove spacing from navbar
#main-navbar, .navbar-container, nav.navbar {
  margin: 0 !important;
  padding: 0 !important;
  height: 0 !important;
  min-height: 0 !important;
  display: none !important;
}

// Hide breadcrumbs completely on home page and remove any spacing
ds-breadcrumbs {
  display: none !important;
}

// Remove breadcrumb spacing
.nav-breadcrumb, .breadcrumb {
  margin: 0 !important;
  padding: 0 !important;
  display: none !important;
}

// Ensure the new background color is applied to all key areas
html, body, main, app-root, ds-themed-page, ds-community-search, ds-home-news {
  background-color: #f8f4ec !important;
}

// Ensure header has the same background color
header, #main-site-header {
  background-color: #f8f4ec !important;
}

// Import the Akhand-Bold font globally
@font-face {
  font-family: 'Akhand-Bold';
  src: url('../../../assets/fonts/Akhand-Bold-BF642a3156ce54d.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
}

// Global styling for all browse category elements
.comcol-browse-label,
.comcol-browse .list-group-item,
.browse-by-metadata h1,
.browse-by-metadata h2,
.browse-by-metadata h3,
.browse-by-metadata h4,
.browse-by-metadata h5,
.browse-by-metadata h6,
nav[aria-label*="Browse"] a,
nav[aria-label*="Browse"] .list-group-item,
.list-group-horizontal .list-group-item,
[class*="browse"] .list-group-item,
[class*="browse"] .nav-link,
[class*="browse"] .btn {
  font-family: 'Akhand-Bold', Arial, sans-serif !important;
  font-weight: bold !important;
}

// Specific styling for browse category tabs
.list-group-horizontal .list-group-item {
  font-family: 'Akhand-Bold', Arial, sans-serif !important;
  font-weight: bold !important;
  font-size: 16px !important;
  color: #2b4878 !important;

  &:hover, &:focus, &.active {
    background-color: #2b4878 !important;
    color: white !important;
    border-color: #2b4878 !important;
  }
}

// Browse section headings
h1, h2, h3, h4, h5, h6 {
  &[class*="browse"],
  &.comcol-browse-label {
    font-family: 'Akhand-Bold', Arial, sans-serif !important;
    font-weight: bold !important;
    color: #2b4878 !important;
  }
}

// Additional footer height reduction for home page
ds-themed-home-page footer,
ds-home-page footer {
  padding: 0.8rem 0 0.3rem 0 !important; // Even more compact for home page

  .bottom-footer {
    padding: 0.3rem 0 0.2rem 0 !important;
    margin-top: 0.5rem !important;
  }

  h5 {
    margin-bottom: 0.3rem !important;
  }

  .logo-column {
    margin-bottom: 0.8rem !important;
  }
}
