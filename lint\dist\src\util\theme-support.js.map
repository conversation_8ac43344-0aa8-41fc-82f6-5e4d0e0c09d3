{"version": 3, "file": "theme-support.js", "sourceRoot": "", "sources": ["../../../src/util/theme-support.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;AAEH,oDAAoD;AACpD,2BAAkC;AAClC,+BAAgC;AAChC,4DAA4C;AAE5C,uCAGmB;AACnB,6CAGsB;AAetB,SAAS,2BAA2B,CAAC,IAAa;IAChD,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QACjG,MAAM,SAAS,GAAG,IAAoB,CAAC;QAEvC,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAC/D,MAAM,MAAM,GAAG,SAAS,CAAC,UAA+B,CAAC;YAEzD,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBACxD,OAAQ,MAAM,CAAC,UAAyB,CAAC,IAAI,KAAK,WAAW,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAqB,EAAE,cAAsB;IAC1E,OAAO,oBAAE,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,OAAgB,EAAE,EAAE;QAClD,IAAI,OAAO,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,iBAAiB,GAAG,OAA+B,CAAC;YAE1D,IAAI,iBAAiB,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACvF,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,EAAE,aAAgC,CAAC;gBAEtF,KAAK,MAAM,OAAO,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;oBAC5C,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACzC,OAAO,iBAAiB,CAAC;oBAC3B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,0BAA0B;IAO9B;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;IACjC,CAAC;IAEM,UAAU,CAAC,MAAM,GAAG,EAAE;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,SAAS,eAAe,CAAC,IAAY;YACnC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAE/B,SAAS,QAAQ,CAAC,IAAa;gBAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnE,MAAM,SAAS,GAAG,IAAI,CAAC,MAA6B,CAAC;oBAErD,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,IAAI,SAAS,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;wBAC5E,OAAO;oBACT,CAAC;oBAED,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,EAAE,WAAqB,CAAC;oBAE3D,KAAK,MAAM,cAAc,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;wBACvD,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;4BACxC,IAAK,IAAY,CAAC,UAAU,CAAC,WAAW,KAAK,iBAAiB,EAAE,CAAC;gCAC/D,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,2BAA2B,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;oCAChG,SAAS;gCACX,CAAC;gCAED,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAyB,CAAC;gCACnE,MAAM,SAAS,GAAI,YAAY,CAAC,QAA0B,EAAE,WAAW,CAAC;gCAExE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oCAC5B,SAAS;gCACX,CAAC;gCAED,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gCAEnE,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;oCACpC,SAAS;gCACX,CAAC;gCAED,MAAM,QAAQ,GAAG,gBAAgB,CAAE,iBAAiB,CAAC,eAAoC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gCAEtG,2BAAmB,CAAC,GAAG,CAAC;oCACtB,SAAS;oCACT,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oCACxD,YAAY,EAAE,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;oCACrD,YAAY;oCACZ,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oCACvD,eAAe,EAAE,IAAA,eAAQ,EAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;iCACrD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;oBACH,CAAC;oBAED,OAAO;gBACT,CAAC;qBAAM,CAAC;oBACN,oBAAE,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE7B,iDAAiD;QACjD,MAAM,QAAQ,GAAa,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,kCAAkC,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAC,KAAK,CAAC;QAE3H,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,eAAe,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,GAAG,CAAC,KAAsC;QAChD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;CACF;AAEY,QAAA,mBAAmB,GAAG,IAAI,0BAA0B,EAAE,CAAC;AAEpE;;;GAGG;AACH,SAAS,SAAS,CAAC,IAAY;IAC7B,OAAO,oBAAE,CAAC,gBAAgB,CACxB,IAAI,EACJ,IAAA,iBAAY,EAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAC7B,oBAAE,CAAC,YAAY,CAAC,MAAM,EAAG,oCAAoC;IAC7D,mBAAmB,CAAC,IAAI,CACzB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAY,EAAE,UAAkB;IACxD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO;YACL,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;SACzB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAED,SAAgB,wBAAwB,CAAC,aAAiC;IACxE,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QAC3E,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,aAAa,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,KAAK,gBAAQ,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACjF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAQ,aAAa,CAAC,MAAM,CAAC,UAAkB,EAAE,IAAI,KAAK,iBAAiB,CAAC;AAC9E,CAAC;AAVD,4DAUC;AAED,SAAgB,yBAAyB,CAAC,aAAiC;IACzE,MAAM,YAAY,GAAG,IAAA,+BAAqB,EAAC,aAAa,CAAC,CAAC;IAE1D,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/B,OAAO;IACT,CAAC;IAED,2BAAmB,CAAC,UAAU,EAAE,CAAC;IACjC,MAAM,KAAK,GAAG,2BAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAEnE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,KAAK,CAAC,SAAS,CAAC;AACzB,CAAC;AAfD,8DAeC;AAED,SAAgB,oBAAoB,CAAC,SAAiB;IACpD,2BAAmB,CAAC,UAAU,EAAE,CAAC;IACjC,OAAO,2BAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACxD,CAAC;AAHD,oDAGC;AAED,SAAgB,6BAA6B,CAAC,QAAgB;IAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IAE/D,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IACD,2BAAmB,CAAC,UAAU,EAAE,CAAC;IACjC,yBAAyB;IACzB,OAAO,2BAAmB,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;AATD,sEASC;AAED,SAAgB,sBAAsB;IACpC,2BAAmB,CAAC,UAAU,EAAE,CAAC;IACjC,OAAO,CAAC,GAAG,2BAAmB,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC;AAHD,wDAGC;AAED,SAAgB,gCAAgC,CAAC,SAAiB;IAChE,2BAAmB,CAAC,UAAU,EAAE,CAAC;IACjC,OAAO,2BAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACxD,CAAC;AAHD,4EAGC;AAED,SAAgB,sBAAsB,CAAC,SAA8B;IACnE,OAAO,IAAA,qCAAwB,EAAC,SAAS,CAAC,IAAI,IAAA,mCAAsB,EAAC,SAAS,CAAC,IAAI,IAAA,2BAAiB,EAAC,SAAS,CAAC,CAAC;AAClH,CAAC;AAFD,wDAEC;AAEY,QAAA,0BAA0B,GAAG,mBAAmB,CAAC;AAE9D,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;AACtD,CAAC;AAFD,oCAEC"}