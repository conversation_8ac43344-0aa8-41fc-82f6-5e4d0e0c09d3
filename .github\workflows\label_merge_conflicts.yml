# This workflow checks open PRs for merge conflicts and labels them when conflicts are found
name: Check for merge conflicts

# Run this for all pushes (i.e. merges) to 'main' or maintenance branches
on:
  push:
    branches:
      - main
      - 'dspace-**'
  # So that the `conflict_label_name` is removed if conflicts are resolved,
  # we allow this to run for `pull_request_target` so that github secrets are available.
  pull_request_target:
    types: [ synchronize ]

permissions: {}

jobs:
  triage:
    # Ensure this job never runs on forked repos. It's only executed for 'dspace/dspace-angular'
    if: github.repository == 'dspace/dspace-angular'
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      # See: https://github.com/prince-chrismc/label-merge-conflicts-action
      - name: Auto-label PRs with merge conflicts
        uses: prince-chrismc/label-merge-conflicts-action@v3
        # Ignore any failures -- may occur (randomly?) for older, outdated PRs.
        continue-on-error: true
        # Add "merge conflict" label if a merge conflict is detected. Remove it when resolved.
        # Note, the authentication token is created automatically
        # See: https://docs.github.com/en/actions/configuring-and-managing-workflows/authenticating-with-the-github_token
        with:
          conflict_label_name: 'merge conflict'
          github_token: ${{ secrets.GITHUB_TOKEN }}
          conflict_comment: |
            Hi @${author},
            Conflicts have been detected against the base branch.
            Please [resolve these conflicts](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/addressing-merge-conflicts/about-merge-conflicts) as soon as you can. Thanks!